import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  SendOption,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';

export class CreateTransactionDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  chainId?: number;

  @ApiProperty({ enum: TransactionType })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({ enum: TransactionSubType })
  @IsEnum(TransactionSubType)
  subType: TransactionSubType;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  questId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  articleId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  userAssetId: number;

  @ApiPropertyOptional({ type: Number })
  @IsOptional()
  @IsNumber()
  assetId: number;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsNumber()
  redeemItemId: number;

  @IsOptional()
  @IsEmail()
  receiverEmail: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  receiveNex3Wallet: string;

  @ApiPropertyOptional({ type: String })
  @IsOptional()
  @IsString()
  amount: string;
}
