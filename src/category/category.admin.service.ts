import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AdminPayload } from 'src/entities/admin.entity';
import { Category } from 'src/entities/category.entity';
import { Repository } from 'typeorm';
import { CreateCategoryAdmin } from './dto/create-category-for-admin';
import { ApiError } from 'src/common/api';
import { UpdateCategoryAdminDto } from './dto/update-category.dto';
import { Article } from 'src/entities/article.entity';
import { GetListCategoryDto } from './dto/get-list-category.dto';
import { Utils } from 'src/common/utils';
import { plainToInstance } from 'class-transformer';
import { CategoryResponseDto } from './dto/category-response.dto';

@Injectable()
export class AdminCategoryService {
  private readonly logger = new Logger(AdminCategoryService.name);

  constructor(
    @InjectRepository(Category)
    private readonly categoryAdminRepository: Repository<Category>,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
  ) {}

  async getAllCategoryByAdmin(requesData: GetListCategoryDto) {
    const queryBuilder = this.categoryAdminRepository
      .createQueryBuilder('categories')
      .orderBy('categories.createAt', 'DESC');

    queryBuilder.where('categories.isDeleted = false');

    const data = await Utils.paginate<any>(queryBuilder, requesData, true);

    return {
      ...data,
      items: plainToInstance(CategoryResponseDto, data.items, {
        excludeExtraneousValues: true,
      }),
    };
  }

  async createCategoryByAdmin(
    currentUser: AdminPayload,
    requestData: CreateCategoryAdmin,
  ): Promise<Category> {
    const category = this.categoryAdminRepository.create({
      ...requestData,
      createdBy: currentUser.id,
    });

    return await this.categoryAdminRepository.save(category);
  }

  async update(
    currentUser: AdminPayload,
    id: number,
    requestData: UpdateCategoryAdminDto,
  ) {
    const updateCategory = await this.categoryAdminRepository.findOne({
      where: {
        id,
        isDeleted: false,
      },
    });

    if (!updateCategory) {
      throw ApiError('', `Update with ID ${id} not found`);
    }

    await this.categoryAdminRepository.update(id, {
      ...requestData,
      updatedBy: currentUser.id,
    });
    return {
      message: `update Category with ${id} successfully`,
    };
  }

  async delete(currentUser: AdminPayload, id: number) {
    const category = await this.categoryAdminRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!category) {
      throw ApiError('', `Category with ID ${id} not found`);
    }

    await this.articleRepository
      .createQueryBuilder()
      .update()
      .set({ categoryId: null })
      .where('categoryId = :id', { id })
      .execute();

    await this.categoryAdminRepository.update(id, {
      isDeleted: true,
      updatedBy: currentUser.id,
    });

    return { message: `Delete category with ID ${id} successfully` };
  }
}
