import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { Category } from 'src/entities/category.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import { GetListCategoryDto } from './dto/get-list-category.dto';
import { Utils } from 'src/common/utils';
// import { ArticleStatus } from 'src/entities/article.entity';
import { plainToInstance } from 'class-transformer';
import { CategoryDetailResponseDto } from './dto/category-detail-response.dto';
import { QuestStatusByDate } from 'src/entities/quest.entity';
import { UserQuestStatus } from 'src/entities/user-quest.entity';
import { level } from 'winston';
import { CategoryResponseDto } from './dto/category-response.dto';
import { ArticleStatus } from 'src/entities/enum.article.entity';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  async getAllCategories(
    currentUser: UserPayload,
    requestData: GetListCategoryDto,
  ) {
    const { page, limit, sort, projection, keyword } = requestData;
    let queryBuilder = this.categoryRepository
      .createQueryBuilder('categories')
      .distinct(true)
      .leftJoin('categories.articleBrands', 'article')
      .leftJoin('article.brand', 'brand')
      .leftJoinAndSelect(
        'article.userArticleAnswers',
        'userArticleAnswers',
        'userArticleAnswers.userId = :userId',
        { userId: currentUser.id },
      )
      .select([
        'categories.id',
        'categories.name',
        'categories.logoUrl',
        'article',
        'brand',
        'categories.createdAt',
        'categories.updatedAt',
        'categories.createdBy',
        'categories.updatedBy',
        'categories.description',
        'categories.contractAddress',
      ]);

    queryBuilder.groupBy('categories.id');

    queryBuilder.where('categories.isDeleted = false');

    queryBuilder.addSelect((subQuery) => {
      return subQuery
        .select('COUNT(article.id)', 'cnt')
        .from('articles', 'article')
        .where('article.categoryId = categories.id');
    }, 'activeArticleCount');

    queryBuilder
      .orderBy('activeArticleCount', 'DESC')
      .addOrderBy('categories.createdAt', 'DESC')
      .addOrderBy('categories.name', 'ASC');

    if (keyword) {
      queryBuilder = Utils.applySearch(queryBuilder, keyword, [
        { name: 'categories.name', isAbsolute: false },
        { name: 'categories.description', isAbsolute: false },
        { name: 'article.title', isAbsolute: false },
        { name: 'brand.name', isAbsolute: false },
      ]);
    }

    const data = await Utils.paginate(
      queryBuilder,
      { page, limit, sort, projection },
      true,
    );

    const result = { items: [], meta: data.meta };
    if (data && data.items.length) {
      const mapped = data.items.map((item) => ({
        ...item,
        activeArticleCount: Number(item['activeArticleCount']),
      }));

      result.items = plainToInstance(CategoryResponseDto, mapped, {
        excludeExtraneousValues: true,
      });
    }

    return result;
  }

  async findOneById(id: number): Promise<CategoryDetailResponseDto> {
    const category = await this.categoryRepository
      .createQueryBuilder('categories')
      .leftJoinAndSelect('categories.articleBrands', 'articleBrands')
      .where('categories.id = :id AND categories.isDeleted = false', { id })
      .getOne();

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return plainToInstance(
      CategoryDetailResponseDto,
      {
        ...category,
        articles: category.articleBrands,
      },
      { excludeExtraneousValues: true },
    );
  }
}
