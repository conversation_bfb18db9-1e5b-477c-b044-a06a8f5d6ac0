import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CategoryService } from './category.service';
import { CategoryController } from './category.controller';
import { Category } from 'src/entities/category.entity';
import { User } from 'src/entities/user.entity';
import { CommonModule } from 'src/common-services/common.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { AdminCategoryService } from './category.admin.service';
import { AdminCategoryController } from './category.admin.controller';
import { Article } from 'src/entities/article.entity';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET_MERCHANT,
      signOptions: { expiresIn: process.env.JWT_EXPIRATION_TIME },
    }),
    TypeOrmModule.forFeature([Category, User, Article]),
    CommonModule,
  ],
  controllers: [CategoryController, AdminCategoryController],
  providers: [CategoryService, AdminCategoryService],
  exports: [CategoryService],
})
export class CategoryModule {}
