import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { JwtAdminAuthGuard } from 'src/auth/guard/jwt-admin-auth.guard';
import { AdminCategoryService } from './category.admin.service';
import { User } from 'src/common/decorator/user.decorator';
import { AdminPayload } from 'src/entities/admin.entity';
import { CreateCategoryAdmin } from './dto/create-category-for-admin';
import { UpdateCategoryAdminDto } from './dto/update-category.dto';
import { GetListCategoryDto } from './dto/get-list-category.dto';
import { Pagination } from 'nestjs-typeorm-paginate';
import { CategoryResponseDto } from './dto/category-response.dto';

@Controller('admin/categories')
@UseGuards(JwtAdminAuthGuard)
export class AdminCategoryController {
  constructor(private readonly categoryAdminService: AdminCategoryService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  async getAll(
    @Query() requestData: GetListCategoryDto,
  ): Promise<Pagination<CategoryResponseDto>> {
    return await this.categoryAdminService.getAllCategoryByAdmin(requestData);
  }

  @Post()
  async createCategory(
    @User() currentUser: AdminPayload,
    @Body() requestData: CreateCategoryAdmin,
  ) {
    return await this.categoryAdminService.createCategoryByAdmin(
      currentUser,
      requestData,
    );
  }

  @Put(':id')
  async updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() requesData: UpdateCategoryAdminDto,
    @User() currentUser: AdminPayload,
  ) {
    return await this.categoryAdminService.update(currentUser, id, requesData);
  }

  @Delete(':id')
  async deleteCategory(
    @Param('id', ParseIntPipe) id: number,
    @User() currentUser: AdminPayload,
  ) {
    return this.categoryAdminService.delete(currentUser, id);
  }
}
