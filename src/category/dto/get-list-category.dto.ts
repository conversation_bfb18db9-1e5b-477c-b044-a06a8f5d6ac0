import {
  IsEnum,
  IsOptional,
  Validate,
  IsDateString,
  IsString,
} from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { SearchDto } from 'src/common/dto.common';
import { QuestStatusByDate } from 'src/entities/quest.entity';
import { EnumQueryValidate } from 'src/common/validate/enum-query.validate';
import { UserQuestStatus } from 'src/entities/user-quest.entity';
import { UserArticleAnswerStatus } from 'src/entities/user-article-answer.entity';

export class GetListCategoryDto extends SearchDto {
  @ApiPropertyOptional({ type: String, format: 'date-time' })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}
