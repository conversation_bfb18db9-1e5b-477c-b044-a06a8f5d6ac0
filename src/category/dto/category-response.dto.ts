import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { Article } from 'src/entities/article.entity';
import { Brand } from 'src/entities/brand.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';
import { UserQuest } from 'src/entities/user-quest.entity';

export class CategoryResponseDto {
  @ApiProperty()
  @Expose({ name: 'categories_id' })
  id: number;

  @ApiProperty()
  @Expose({ name: 'categories_name' })
  name: string;

  @ApiProperty({ required: false })
  @Expose({ name: 'categories_description' })
  description?: string;

  @ApiProperty()
  @Expose({ name: 'categories_logoUrl' })
  logoUrl: string;

  @ApiProperty()
  @Expose({ name: 'categories_createdAt' })
  createdAt: Date;

  @ApiProperty()
  @Expose({ name: 'categories_updateAt' })
  updatedAt: Date;

  @ApiProperty()
  @Expose({ name: 'categories_createdBy' })
  createdBy: Date;

  @ApiProperty()
  @Expose({ name: 'categories_updatedBy' })
  updatedBy: Date;

  @ApiProperty({ required: false })
  @Expose({ name: 'categories_contractAddress' })
  contractAddress?: string;

  @ApiProperty({ type: () => Article, required: false })
  @Expose()
  @Transform(({ obj }) => {
    const prefix = 'article_';
    if (!obj[`${prefix}id`]) {
      return null;
    }

    const article = {};
    for (const key in obj) {
      if (key.startsWith(prefix)) {
        article[key.substring(prefix.length)] = obj[key];
      }
    }
    return article;
  })
  article?: Article;

  @ApiProperty({ type: () => Brand, required: false })
  @Expose()
  @Transform(({ obj }) => {
    const prefix = 'brand_';
    if (!obj[`${prefix}id`]) {
      return null;
    }

    const brand = {};
    for (const key in obj) {
      if (key.startsWith(prefix)) {
        brand[key.substring(prefix.length)] = obj[key];
      }
    }
    return brand;
  })
  brand: Brand;

  @ApiProperty()
  @Expose()
  activeArticleCount: number;
}
