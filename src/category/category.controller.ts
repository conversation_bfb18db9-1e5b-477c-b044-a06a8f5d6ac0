import {
  Controller,
  Body,
  Get,
  HttpCode,
  UseGuards,
  HttpStatus,
  Param,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { UserPayload } from 'src/entities/user.entity';
import { CategoryService } from './category.service';
import { GetListCategoryDto } from './dto/get-list-category.dto';
import { User } from 'src/common/decorator/user.decorator';
import { Pagination } from 'nestjs-typeorm-paginate';
import { CategoryResponseDto } from './dto/category-response.dto';
import { CategoryDetailResponseDto } from './dto/category-detail-response.dto';

@ApiBearerAuth()
@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get categories' })
  async getAllCategories(
    @User() currentUser: UserPayload,
    @Query() requestData: GetListCategoryDto,
  ): Promise<Pagination<CategoryResponseDto>> {
    return this.categoryService.getAllCategories(currentUser, requestData);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get detail category by id' })
  async findOneById(
    @Param('id') id: number,
  ): Promise<CategoryDetailResponseDto> {
    return this.categoryService.findOneById(id);
  }
}
