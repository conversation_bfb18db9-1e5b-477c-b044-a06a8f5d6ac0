import { IsEnum, IsOptional, IsDateString } from 'class-validator';
import { SearchDto } from 'src/common/dto.common';
import { ArticleStatus } from 'src/entities/enum.article.entity';
import { QuestStatus } from 'src/entities/quest.entity';

export enum TypeFilter {
  ARTICLE = 'article',
  QUEST = 'quest',
}

export class GetQuestsListDto extends SearchDto {
  @IsDateString()
  @IsOptional()
  fromDate?: string;

  @IsDateString()
  @IsOptional()
  toDate?: string;

  @IsEnum(ArticleStatus)
  @IsOptional()
  articleStatus?: ArticleStatus;

  @IsEnum(QuestStatus)
  @IsOptional()
  questStatus?: QuestStatus;

  @IsEnum(TypeFilter)
  typeFilter: TypeFilter;
}
