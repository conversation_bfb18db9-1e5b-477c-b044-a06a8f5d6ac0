/* eslint-disable max-lines-per-function */
import { Injectable, Logger } from '@nestjs/common';
import BigNumber from 'bignumber.js';
import { Utils } from 'src/common/utils';
import { Chain, ChainType } from 'src/entities/chain.entity';
import {
  Web3Account,
  Web3CallViewMethodAptos,
  Web3CallWriteMethodAptos,
  Web3ContractParam,
  Web3Event,
  Web3Service,
  Web3SignedTransaction,
  Web3Token,
  Web3Transaction,
  TransactionEvent,
  Web3Signature,
} from '../web3.interface';

import { InjectRepository } from '@nestjs/typeorm';
import { Asset, AssetType, TokenType } from 'src/entities/asset.entity';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { PaidType, RedeemItem } from 'src/entities/redeem-item.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import { Transaction, TransactionType } from 'src/entities/transaction.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { User<PERSON>hain } from 'src/entities/user-chain.entity';
import { DataSource, Repository } from 'typeorm';
import {
  Network,
  Account,
  Ed25519PrivateKey,
  Aptos,
  AptosConfig,
  AccountAuthenticator,
  Serializer,
  AccountAddress,
  Hex,
  PrivateKey,
  PrivateKeyVariants,
} from '@aptos-labs/ts-sdk';
import { getContractMethods } from 'src/common/constants';
const crypto = require('crypto');
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

// Aptos SDK imports
// Note: Install @aptos-labs/ts-sdk first: npm install @aptos-labs/ts-sdk
/* eslint-disable @typescript-eslint/no-unused-vars */

@Injectable()
export class Web3AptosService implements Web3Service {
  private readonly logger = new Logger(Web3AptosService.name);

  constructor(
    private dataSource: DataSource,
    @InjectRepository(HotWallet)
    private hotWalletRepository: Repository<HotWallet>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
    @InjectRepository(Asset)
    private assetRepository: Repository<Asset>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
  ) {}

  chain: Chain;
  aptos: Aptos;

  init(chain: Chain) {
    this.chain = chain;
    this.setProvider();
  }

  getChainType(): ChainType {
    return ChainType.MOVEVM;
  }

  setProvider() {
    const rpc = Utils.getRandom(this.chain.rpcs.split(',')).trim();
    this.logger.debug(`Network ${this.chain.chainName}. RPC = ${rpc}`);

    const config = new AptosConfig({
      network: Network.TESTNET,
      // fullnode: rpc,
    });
    this.aptos = new Aptos(config);
  }

  private getSigner(): Account {
    if (!this.chain.privateKey) {
      throw new Error('No signer found');
    }

    const privateKeyBytes = Utils.decrypt(this.chain.privateKey);
    const formatted = PrivateKey.formatPrivateKey(
      privateKeyBytes,
      PrivateKeyVariants.Ed25519,
    );
    const privateKey = new Ed25519PrivateKey(formatted);
    return Account.fromPrivateKey({ privateKey });
  }

  private async isHotwalletPending(hotwallet: HotWallet): Promise<boolean> {
    try {
      const pendingTxs = await this.aptos.getAccountTransactions({
        accountAddress: hotwallet.walletAddress,
        options: {
          limit: 10,
          offset: 0,
          // orderBy: [{ transactionVersion: 'desc' }],
        },
      });

      const hasPendingTx = pendingTxs.some(
        (tx: any) => tx.type === 'pending_transaction',
      );

      this.logger.debug(
        `Wallet ${hotwallet.walletAddress} is ${
          hasPendingTx ? 'pending' : 'available'
        }`,
      );

      return hasPendingTx;
    } catch (error) {
      this.logger.warn(
        `Failed to check pending status for wallet ${hotwallet.walletAddress}: ${error.message}`,
      );
      return false;
    }
  }

  private async getHotwallet(requestData: Web3CallWriteMethodAptos) {
    const {
      method,
      contractAddress,
      typeParams = [],
      params,
      sender,
    } = requestData;
    // 1) List hotwallets (fee payer)
    const feePayerCandidates = await this.hotWalletRepository.find({
      where: { chainId: this.chain.id },
    });

    if (!feePayerCandidates.length) {
      throw new Error('No hotwallet found');
    }

    // 2) Wallet bootstrap for simulation
    const bootstrapWallet = feePayerCandidates[0];
    const [bootstrapSigner, signer] = await Promise.all([
      this.getAccount(bootstrapWallet.privateKey),
      this.getAccount(sender.privateKey),
    ]);

    // 3) Simulate gasUsed/gasUnitPrice
    let gasUsedEstimate = '0';
    let gasUnitPriceEstimate = '0';
    try {
      const simulationTransaction = await this.aptos.transaction.build.simple({
        sender: signer.accountAddress,
        withFeePayer: true,
        data: {
          function: method as `${string}::${string}::${string}`,
          typeArguments: typeParams,
          functionArguments: params,
        },
      });

      const [simulationResult] = await this.aptos.transaction.simulate.simple({
        signerPublicKey: signer.publicKey,
        transaction: simulationTransaction,
      });

      gasUsedEstimate = String((simulationResult as any).gas_used ?? '0');
      gasUnitPriceEstimate = String(
        (simulationResult as any).gas_unit_price ?? '0',
      );
    } catch (error: any) {
      throw new Error('Estimate gas failed: ' + (error?.message || error));
    }

    // 4) Fee budget (octas) = 2 × (gas_used × gas_unit_price)

    const feeEstimateOctas =
      Utils.toBigNumber(gasUsedEstimate).multipliedBy(gasUnitPriceEstimate);
    const feeBudgetOctas = feeEstimateOctas
      .multipliedBy(1.5)
      .decimalPlaces(0)
      .toString();

    // 5) Select fee payer with enough balance
    const walletBalances = await Promise.all(
      feePayerCandidates.map(async (wallet) => {
        const balanceOctas = await this.getBalance(wallet.walletAddress);
        this.logger.debug(
          `getHotwallet(): address=${wallet.walletAddress}, balance=${balanceOctas}`,
        );
        return { wallet, balanceOctas };
      }),
    );

    const eligibleWallets = walletBalances
      .filter(({ balanceOctas }) =>
        Utils.toBigNumber(balanceOctas).gte(feeBudgetOctas),
      )
      .map(({ wallet }) => wallet);

    if (!eligibleWallets.length) {
      throw new Error('No hotwallet has enough balance');
    }

    // 6) Pick random hotwallet that is not pending
    while (eligibleWallets.length > 0) {
      const selectionIndex = Math.floor(Math.random() * eligibleWallets.length);
      const feePayerWallet = eligibleWallets[selectionIndex];

      const pendingStatus = await this.isHotwalletPending(feePayerWallet);
      if (!pendingStatus) {
        return {
          hotwallet: feePayerWallet,
          gasLimit: feeBudgetOctas,
          gasUnitPrice: gasUnitPriceEstimate,
          signer: bootstrapSigner,
        };
      }
      eligibleWallets.splice(selectionIndex, 1);
    }
    throw new Error('No hotwallet is available');
  }

  signTypedMessage(params: Web3ContractParam[]) {
    const getMessage = (params: Web3ContractParam[]) => {
      const serializer = new Serializer();
      for (let index = 0; index < params.length; index++) {
        const param = params[index];
        switch (param.paramType) {
          case 'u8':
            serializer.serializeU8(param.paramValue);
            break;
          case 'number':
            serializer.serializeU64(param.paramValue);
            break;
          case 'string':
            serializer.serializeStr(param.paramValue);
            break;
          case 'array':
            serializer.serializeU32AsUleb128(param.paramValue.length);
            for (const item of param.paramValue) {
              serializer.serializeU64(item);
            }
            break;
          case 'array256':
            serializer.serializeU32AsUleb128(param.paramValue.length);
            for (const item of param.paramValue) {
              serializer.serializeU256(item);
            }
            break;
          case 'address':
            const accountAddress = AccountAddress.fromString(param.paramValue);
            accountAddress.serialize(serializer);
            break;
          case 'boolean':
            serializer.serializeBool(param.paramValue);
            break;
          case 'object':
            const cleanRewardMetadata = param.paramValue.startsWith('0x')
              ? param.paramValue.slice(2)
              : param.paramValue;
            const rewardMetadataBytes = new Uint8Array(
              Buffer.from(cleanRewardMetadata.padStart(64, '0'), 'hex'),
            );
            serializer.serializeFixedBytes(rewardMetadataBytes);
            break;
          default:
            throw new Error('Unsupported param type: ' + param.paramType);
        }
      }

      return crypto
        .createHash('sha3-256')
        .update(serializer.toUint8Array())
        .digest();
    };

    const signer = this.getSigner();
    const messageBytes = getMessage(params);
    const signature = signer.sign(messageBytes);

    return {
      messageHash: Hex.fromHexInput(messageBytes).toString(),
      signature: signature,
    };
  }

  verifySignMessage(
    originalMessage: string,
    signature: string,
    address: string,
  ): boolean {
    // Implement Aptos signature verification
    // This is a placeholder implementation
    try {
      return true; // Simplified for now
    } catch (error) {
      return false;
    }
  }

  async getToken(tokenAddress: string): Promise<Web3Token> {
    try {
      // In Aptos, tokens are handled differently through coin modules
      // This is a simplified implementation
      const coinInfo = await this.aptos.view({
        payload: {
          function: '0x1::coin::coin_info',
          functionArguments: [],
          typeArguments: [tokenAddress],
        },
      });

      return {
        address: tokenAddress,
        name: coinInfo[0] as string,
        symbol: coinInfo[1] as string,
        decimals: coinInfo[2] as number,
      };
    } catch (error) {
      throw new Error(`Failed to get token info: ${error.message}`);
    }
  }

  async getTransaction(txId: string): Promise<Web3Transaction> {
    try {
      const transaction = await this.aptos.getTransactionByHash({
        transactionHash: txId,
      });

      if (!transaction) {
        throw new Error(`Transaction ${txId} not found`);
      }

      return this.convertAptosToWeb3Transaction(transaction);
    } catch (error) {
      throw new Error(`Failed to get transaction: ${error.message}`);
    }
  }

  private convertAptosToWeb3Transaction(transaction: any): Web3Transaction {
    return {
      txHash: transaction.hash,
      status: transaction.success || false,
      from: transaction.sender,
      to: transaction.payload?.function || '',
      gasPrice: new BigNumber(transaction.gas_unit_price || 0),
      gasUsed: new BigNumber(transaction.gas_used || 0),
      fee: new BigNumber(transaction.gas_used || 0).multipliedBy(
        transaction.gas_unit_price || 0,
      ),
      contractAddress: transaction.payload?.function?.split('::')[0] || '',
      payload: transaction.payload,
      logs: transaction.events || [],
      events: this.parseAptosEvents(transaction.events || []),
    };
  }

  private parseAptosEvents(events: any[]): Web3Event[] {
    return events.map((event) => ({
      name: event.type,
      ...event.data,
    }));
  }

  parseLogs(logs: any[], abi: any[]): Web3Event[] {
    // Aptos events are already structured, unlike EVM logs that need ABI decoding
    return this.parseAptosEvents(logs);
  }

  async getBalance(address: string): Promise<BigNumber> {
    try {
      const balance = await this.aptos.getAccountAPTAmount({
        accountAddress: address,
      });
      return new BigNumber(balance);
    } catch (error) {
      this.logger.warn(
        `Failed to get balance for ${address}: ${error.message}`,
      );
      return new BigNumber(0);
    }
  }

  async getTokenBalance(asset: Asset, address: string): Promise<BigNumber> {
    try {
      if (asset.isNativeToken) {
        return this.getBalance(address);
      } else {
        const balance = await this.aptos.getAccountCoinAmount({
          accountAddress: address,
          coinType:
            `${asset.contractAddress}` as `${string}::${string}::${string}`,
        });
        return new BigNumber(balance);
      }
    } catch (error) {
      this.logger.warn(`Failed to get token balance: ${error.message}`);
      return new BigNumber(0);
    }
  }

  getAccount(privateKey: string): Account {
    const privateKeyBytes = Utils.decrypt(privateKey);
    const formatted = PrivateKey.formatPrivateKey(
      privateKeyBytes,
      PrivateKeyVariants.Ed25519,
    );
    const privateKeyObj = new Ed25519PrivateKey(formatted);
    const account = Account.fromPrivateKey({ privateKey: privateKeyObj });

    // const account = Account.fromPrivateKey({
    //   privateKey: new Ed25519PrivateKey(privateKey),
    // });

    return account;
  }

  createAccount(): Web3Account {
    const account = Account.generate();
    return {
      address: account.accountAddress.toString(),
      privateKey: account.privateKey.toString(),
      publicKey: account.publicKey.toString(),
    };
  }

  async signTransaction(
    requestData: Web3CallWriteMethodAptos,
  ): Promise<Web3SignedTransaction> {
    const { method, contractAddress, typeParams = [], sender } = requestData;
    const { params } = requestData;
    const contractMethod = getContractMethods(contractAddress)[method];
    const { hotwallet, gasLimit, signer, gasUnitPrice } =
      await this.getHotwallet({
        method: contractMethod,
        typeParams,
        contractAddress,
        params,
        sender,
      });

    // const fee = await this._transferGasFee(
    //   signer,
    //   sender.walletAddress,
    //   gasLimit.toString(),
    // );

    try {
      const senderSigner = this.getAccount(sender.privateKey);
      const transaction = await this.aptos.transaction.build.simple({
        sender: senderSigner.accountAddress,
        withFeePayer: true,
        data: {
          function: contractMethod,
          functionArguments: params,
          typeArguments: typeParams,
        },
        options: {
          maxGasAmount: parseInt(gasLimit),
          gasUnitPrice: parseInt(gasUnitPrice),
        },
      });

      const senderAuthenticator = await this.aptos.transaction.sign({
        signer: senderSigner,
        transaction,
      });

      const sponsorAuthenticator = await this.aptos.transaction.signAsFeePayer({
        signer: signer,
        transaction,
      });

      return {
        signedTx: senderAuthenticator,
        signedTxAsFeePayer: sponsorAuthenticator,
        txHash: '', // Will be available after submission
        hotwallet,
        transaction,
      };
    } catch (error) {
      throw new Error(`Failed to sign transaction: ${error.message}`);
    }
  }

  async sendSignedTransaction(
    requestData: Web3SignedTransaction,
  ): Promise<any> {
    const { signedTx, signedTxAsFeePayer, hotwallet, transaction } =
      requestData;

    try {
      // const signedTransaction = JSON.parse(signedTx);
      const pendingTxn = await this.aptos.transaction.submit.simple({
        transaction,
        senderAuthenticator: signedTx as AccountAuthenticator,
        feePayerAuthenticator: signedTxAsFeePayer as AccountAuthenticator,
      });

      const response = await this.aptos.waitForTransaction({
        transactionHash: pendingTxn.hash,
      });

      this.logger.debug(
        `sendSignedTransaction(): txHash = ${response.hash}, hotwallet = ${hotwallet.walletAddress}`,
      );

      return response;
    } catch (error) {
      throw new Error(`Failed to send signed transaction: ${error.message}`);
    }
  }

  private async _transferGasFee(
    hotwalletSigner: Account,
    recipientAddress: string,
    amountInOctas: string,
  ): Promise<any> {
    try {
      // 1. Create payload for transfer APT transaction
      const transaction = await this.aptos.transaction.build.simple({
        sender: hotwalletSigner.accountAddress,
        data: {
          function: '0x1::coin::transfer',
          typeArguments: ['0x1::aptos_coin::AptosCoin'],
          functionArguments: [recipientAddress, amountInOctas],
        },
      });

      // 2. Sign and submit transaction
      const pendingTxn = await this.aptos.signAndSubmitTransaction({
        signer: hotwalletSigner,
        transaction,
      });

      // 3. Wait for transaction to be confirmed on blockchain
      const result = await this.aptos.waitForTransaction({
        transactionHash: pendingTxn.hash,
      });

      console.log(`Chuyển phí gas thành công! Version: ${result.version}`);
      return result;
    } catch (error) {
      console.error('Error transferring gas fee:', error);
      throw new Error(`Cannot transfer gas fee for sender: ${error.message}`);
    }
  }

  async callWriteMethod(requestData: Web3CallWriteMethodAptos): Promise<any> {
    return null;
  }

  async callViewMethod(requestData: Web3CallViewMethodAptos): Promise<any> {
    return null;
  }

  async getTransactionEvent(
    txHash: string,
    assetType: AssetType,
  ): Promise<TransactionEvent> {
    try {
      const transaction: any = await this.aptos.getTransactionByHash({
        transactionHash: txHash,
      });

      if (!transaction) {
        return {
          txHash,
          gasUsed: new BigNumber(0),
          hotWallet: '',
          status: false,
        };
      }

      const events = transaction.events || [];
      let event;
      let eventMint;

      // Map asset types to Aptos event types
      if (assetType === AssetType.NFT_VOUCHER) {
        event = events.find((e: any) => e.type.includes('MintTokenEvent'));
        eventMint = events.find((e: any) => e.type.includes('Mint'));
      } else if (assetType === AssetType.NFT) {
        event = events.find((e: any) => e.type.includes('GrantNFTEvent'));
      } else if (assetType === AssetType.TOKEN) {
        event = events.find((e: any) => e.type.includes('GrantTokenEvent'));
      }

      console.log('assetType', assetType);
      console.log('event', event);

      return {
        txHash,
        transactionId: event?.data?.transactionId,
        tokenId:
          event?.data?.tokenId ||
          event?.data?.token_id ||
          event?.data?.id ||
          '',
        gasUsed: new BigNumber(transaction?.gas_used || 0),
        hotWallet: transaction.sender,
        status: transaction.success || false,
        nftContractAddress: eventMint?.data?.token || '',
      };
    } catch (error) {
      throw new Error(`Failed to get transaction event: ${error.message}`);
    }
  }

  async createSignedTransaction(
    transaction: Transaction,
  ): Promise<Web3SignedTransaction> {
    // This is a simplified implementation for Aptos
    // The actual implementation would depend on your specific Aptos smart contracts

    let signedTx: Web3SignedTransaction;

    const [sender, receiver, asset, sponsor] = await Promise.all([
      this.userChainRepository.findOneBy({
        userId: transaction.fromUserId,
        chainId: this.chain.id,
      }),
      this.userChainRepository.findOneBy({
        userId: transaction.toUserId,
        chainId: this.chain.id,
      }),
      this.assetRepository.findOneBy({
        id: transaction.assetId,
      }),
      this.sponsorRepository.findOneBy({
        id: transaction.sponsorId,
      }),
    ]);

    let redeemItem: RedeemItem | null = null;
    if (transaction.type === TransactionType.REDEEM) {
      redeemItem = await this.redeemItemRepository.findOne({
        where: { id: transaction.redeemItemId },
        relations: { asset: true, paidAsset: true, sponsor: true },
      });
    }

    console.log('sender.walletAddress', sender.walletAddress);

    this.logger.log(
      `APTOS createSignedTransaction(): sender = ${sender.walletAddress}, receiver = ${receiver.walletAddress}, transaction = ${JSON.stringify(transaction)}`,
    );

    if (transaction.type === TransactionType.REDEEM && redeemItem) {
      const feeContractAddress = redeemItem?.paidAssetId
        ? redeemItem.paidAsset.contractAddress
        : redeemItem.asset.contractAddress;
      const fee =
        Utils.toBigNumber(transaction.point)
          .multipliedBy(10 ** redeemItem?.paidAsset?.decimals)
          .toString() || '0';
      const rewardContractAddress = redeemItem.asset.contractAddress;
      const amount = Utils.toBigNumber(transaction.redeemedAmount)
        .multipliedBy(10 ** asset.decimals)
        .toString();
      if (redeemItem.asset.type === AssetType.NFT_VOUCHER) {
        const signature = await this._signTypedMessageBymethod('MINT_TOKEN', {
          redeemItem,
          transactionId: transaction.id,
          sender,
          amount,
          fee,
        });
        signedTx = await this.signTransaction({
          method: 'MINT_TOKEN',
          params: [
            feeContractAddress,
            redeemItem.paidAssetId ? fee : 0,
            transaction.id,
            Array.from(signature.signature['data'].data),
          ],
          contractAddress: this.chain.factorySponsorContractAddress,
          sender,
        });
      } else if (redeemItem.asset.type === AssetType.NFT) {
        const nft = await this.userAssetRepository.findOne({
          where: {
            redeemItemId: transaction.redeemItemId,
            id: transaction.nftId,
          },
          relations: { asset: true },
        });

        const signature = await this._signTypedMessageBymethod(
          'GRANT_NFT_FOR_USER',
          {
            nftContractAddress: nft?.nftContractAddress,
            redeemItem,
            transactionId: transaction.id,
            sender,
            amount,
            fee,
          },
        );

        signedTx = await this.signTransaction({
          method: 'GRANT_NFT_FOR_USER',
          params: [
            sponsor.name,
            nft?.nftContractAddress,
            feeContractAddress,
            redeemItem.paidAssetId ? fee : 0,
            transaction.id,
            Array.from(signature.signature['data'].data),
          ],
          contractAddress: this.chain.factorySponsorContractAddress,
          typeParams: [],
          sender,
        });
      } else if (redeemItem.asset.type === AssetType.TOKEN) {
        const signature = await this._signTypedMessageBymethod(
          'GRANT_TOKEN_FOR_USER',
          {
            redeemItem,
            transactionId: transaction.id,
            sender,
            amount,
            fee,
          },
        );

        signedTx = await this.signTransaction({
          method: 'GRANT_TOKEN_FOR_USER',
          params: [
            sponsor.name,
            rewardContractAddress,
            amount,
            feeContractAddress,
            redeemItem.paidAssetId ? fee : 0,
            transaction.id,
            Array.from(signature.signature['data'].data),
          ],
          contractAddress: this.chain.factorySponsorContractAddress,
          typeParams: [],
          sender,
        });
      }
    } else if (transaction.type === TransactionType.TRANSFER) {
      if (asset.type === AssetType.TOKEN) {
        const amountTransfer = Utils.toBigNumber(transaction.point)
          .multipliedBy(10 ** asset.decimals)
          .toString();
        if (asset.subType === TokenType.APTOS_FA) {
          signedTx = await this.signTransaction({
            method: 'TRANSFER_TOKEN',
            params: [
              asset.contractAddress,
              receiver.walletAddress,
              amountTransfer,
            ],
            contractAddress: '0x1',
            sender,
          });
        }
      } else {
        const nft = await this.userAssetRepository.findOne({
          where: {
            id: transaction.nftId,
          },
          relations: { asset: true },
        });

        const aliceDigitalAssets = await this.aptos.getOwnedDigitalAssets({
          ownerAddress: nft.asset.contractAddress,
        });

        signedTx = await this.signTransaction({
          method: 'TRANSFER_NFT',
          params: [nft.nftContractAddress, receiver.walletAddress],
          contractAddress: '0x1',
          sender,
          typeParams: ['0x4::token::Token'],
        });
      }
    }

    return signedTx;
  }

  private async _signTypedMessageBymethod(
    method: string,
    requestData: {
      nftContractAddress?: string;
      redeemItem: RedeemItem;
      transactionId: number;
      sender: UserChain;
      amount: string;
      fee: string;
    },
  ): Promise<Web3Signature> {
    const {
      redeemItem,
      transactionId,
      sender,
      amount,
      fee,
      nftContractAddress,
    } = requestData;

    switch (method) {
      case 'GRANT_TOKEN_FOR_USER':
        return this.signTypedMessage([
          {
            paramType: 'string',
            paramValue: redeemItem.sponsor.name,
          },
          {
            paramType: 'object',
            paramValue: sender.walletAddress,
          },
          {
            paramType: 'object',
            paramValue: redeemItem.asset.contractAddress,
          },
          {
            paramType: 'number',
            paramValue: amount,
          },
          {
            paramType: 'object',
            paramValue: redeemItem.paidAssetId
              ? redeemItem.paidAsset.contractAddress
              : redeemItem.asset.contractAddress,
          },
          {
            paramType: 'number',
            paramValue: redeemItem.paidAssetId ? fee : 0,
          },
          {
            paramType: 'number',
            paramValue: transactionId,
          },
        ]);
      case 'GRANT_NFT_FOR_USER':
        return this.signTypedMessage([
          {
            paramType: 'string',
            paramValue: redeemItem.sponsor.name,
          },
          {
            paramType: 'object',
            paramValue: nftContractAddress,
          },
          {
            paramType: 'object',
            paramValue: sender.walletAddress,
          },
          {
            paramType: 'object',
            paramValue: redeemItem.paidAssetId
              ? redeemItem.paidAsset.contractAddress
              : redeemItem.asset.contractAddress,
          },
          {
            paramType: 'number',
            paramValue: redeemItem.paidAssetId ? fee : 0,
          },
          {
            paramType: 'number',
            paramValue: transactionId,
          },
        ]);
      case 'MINT_TOKEN':
        return this.signTypedMessage([
          {
            paramType: 'object',
            paramValue: sender.walletAddress,
          },
          {
            paramType: 'object',
            paramValue: redeemItem.paidAssetId
              ? redeemItem.paidAsset.contractAddress
              : redeemItem.asset.contractAddress,
          },
          {
            paramType: 'number',
            paramValue: redeemItem.paidAssetId ? fee : 0,
          },
          {
            paramType: 'number',
            paramValue: transactionId,
          },
        ]);
      default:
        throw new Error('Unsupported method: ' + method);
    }
  }

  async setUpDelegation(
    eoaAddress: string,
    privateKey: string,
    chain: Chain,
  ): Promise<boolean> {
    // Aptos doesn't have the same delegation concept as EVM
    // This might involve setting up sponsored transactions or multi-sig
    // For now, return true as a placeholder
    this.logger.debug('Aptos delegation setup - placeholder implementation');
    return true;
  }

  async performRedeemVoucherWithNex3Transaction(
    userAddress: string,
    userPrivateKey: string,
    transaction: Transaction,
    redeemItem: RedeemItem,
  ): Promise<any> {
    return null;
  }
}
