import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { Twitter<PERSON>pi } from 'twitter-api-v2';

@Injectable()
export class TwitterService {
  private readonly logger = new Logger(TwitterService.name);
  private client: <PERSON><PERSON><PERSON>;
  private url: string;

  constructor() {
    this.client = new Twitter<PERSON>pi(process.env.X_BEARER_TOKEN);
    this.url = process.env.X_PREFIX_URL;
  }

  async getCurrentUser(token: string) {
    const options = {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    };

    const userFields = [
      'profile_image_url',
      'created_at',
      'description',
      'location',
      'public_metrics',
      'verified',
    ].join(',');

    const requestUrl = `${this.url}2/users/me?user.fields=${userFields}`;

    try {
      const response = await axios.get(requestUrl, options);

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Axios Error:', error.response?.data || error.message);
      } else {
        console.error('Unexpected Error:', error);
      }
      throw error;
    }
  }
}
