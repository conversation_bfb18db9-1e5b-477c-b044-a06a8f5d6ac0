import { Aptos, AptosConfig, Network, AccountAddress } from "@aptos-labs/ts-sdk";

const FAILING_CONTRACT = '0x3de3e262ef26841b5c04e6b67ba986dbb549a2de485131874473975326259e74';
const WORKING_CONTRACT = '0x869ebbe6679655dc5fccace81bd6d8ffc871f1b6881a26f1611945185e6d62de';
const NEX3_SEED = "Nex3 Seed";

async function checkAdminStates() {
  console.log('🎯 CHECKING ADMINSTATE AT RESOURCE ADDRESSES');
  console.log('='.repeat(80));
  console.log('From the contract code:');
  console.log('  const NEX3_SEED: vector<u8> = b"Nex3 Seed";');
  console.log('  resource_address = object::create_object_address(&@nex3, NEX3_SEED)');
  console.log('='.repeat(80));

  const config = new AptosConfig({ network: Network.TESTNET });
  const aptos = new Aptos(config);

  for (const [label, contractAddress] of [
    ['WORKING', WORKING_CONTRACT],
    ['FAILING', FAILING_CONTRACT]
  ]) {
    console.log(`\n${'█'.repeat(80)}`);
    console.log(`${label} CONTRACT: ${contractAddress}`);
    console.log('█'.repeat(80));

    try {
      // Step 1: Calculate the resource address using the contract's logic
      const seedBytes = new TextEncoder().encode(NEX3_SEED);
      
      const payload = {
        function: "0x1::object::create_object_address",
        type_arguments: [],
        arguments: [contractAddress, Array.from(seedBytes)]
      };
      
      const result = await aptos.view({ payload }) as string[];
      const resourceAddress = result[0];
      
      console.log(`\n📍 Calculated resource address: ${resourceAddress}`);

      // Step 2: Check what resources exist at this address
      try {
        const resources = await aptos.getAccountResources({
          accountAddress: resourceAddress
        });

        console.log(`\n✅ Resource account exists with ${resources.length} resources:`);
        
        for (const resource of resources) {
          console.log(`\n📦 ${resource.type}`);
          
          // Check for AdminState
          if (resource.type.includes('AdminState')) {
            console.log('   🎯 FOUND ADMINSTATE!');
            console.log('   Full data:', JSON.stringify(resource.data, null, 2));
            
            if (resource.data && 'signer_u8_array' in resource.data) {
              const signerArray = resource.data.signer_u8_array as any[];
              
              console.log(`\n   📏 signer_u8_array length: ${signerArray.length} bytes`);
              console.log(`   📝 signer_u8_array:`, signerArray);
              
              if (signerArray.length === 0) {
                console.log(`\n   ❌ ERROR: Public key is EMPTY!`);
                console.log(`   This means set_admin() was never called to set the public key.`);
                console.log(`\n   🔧 FIX: The admin needs to call:`);
                console.log(`   set_admin(admin_signer, new_admin_address, public_key_32_bytes)`);
              } else if (signerArray.length !== 32) {
                console.log(`\n   ❌ ERROR: Invalid public key length!`);
                console.log(`   Expected: 32 bytes (Ed25519 public key)`);
                console.log(`   Got: ${signerArray.length} bytes`);
              } else {
                console.log(`\n   ✅ Public key length is CORRECT (32 bytes)!`);
                console.log(`   This contract should work fine.`);
              }
              
              // Show admin info
              if ('admin' in resource.data) {
                console.log(`\n   👤 Admin address: ${resource.data.admin}`);
              }
              if ('pending_admin' in resource.data) {
                console.log(`   ⏳ Pending admin: ${resource.data.pending_admin}`);
              }
            }
          }
        }
      } catch (resourceError: any) {
        console.error(`\n❌ Error fetching resources: ${resourceError.message}`);
        console.log(`   The resource address might not exist yet (contract not initialized)`);
      }
    } catch (error: any) {
      console.error(`\n❌ Error calculating resource address: ${error.message}`);
    }
  }

  console.log('\n\n' + '█'.repeat(80));
  console.log('SUMMARY');
  console.log('█'.repeat(80));
  console.log(`
The issue occurs because:
1. AdminState is initialized with an EMPTY signer_u8_array (0 bytes)
2. The admin must call set_admin() to set a valid 32-byte Ed25519 public key
3. The failing contract probably never had set_admin() called

To fix this, the admin of the failing contract needs to call:
  
  set_admin(
    signer: &signer,           // The current admin
    new_admin: address,        // Can be same admin or new one
    signer_u8_array: vector<u8> // 32-byte Ed25519 public key
  )
  
This will update the signer_u8_array and allow signature verification to work.
`);
  console.log('='.repeat(80));
}

checkAdminStates();