import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";

// Contracts to check
const FAILING_CONTRACT = '0x3de3e262ef26841b5c04e6b67ba986dbb549a2de485131874473975326259e74';
const WORKING_CONTRACT = '0x869ebbe6679655dc5fccace81bd6d8ffc871f1b6881a26f1611945185e6d62de';

async function listAllResources(aptos: Aptos, address: string, label: string) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`LISTING ALL RESOURCES: ${label}`);
  console.log(`${'='.repeat(80)}`);
  console.log(`Address: ${address}\n`);

  try {
    const resources = await aptos.getAccountResources({
      accountAddress: address
    });

    console.log(`Found ${resources.length} resources:\n`);

    for (const resource of resources) {
      console.log(`📦 ${resource.type}`);
      
      // Check if this looks like an AdminState
      if (resource.type.includes('AdminState')) {
        console.log('   ⭐ Found AdminState!');
        console.log('   Data:', JSON.stringify(resource.data, null, 2));
        
        if (resource.data && 'signer_u8_array' in resource.data) {
          const signerArray = resource.data.signer_u8_array as any[];
          console.log(`   📏 signer_u8_array length: ${signerArray.length} bytes`);
          console.log(`   📝 signer_u8_array:`, signerArray);
          
          if (signerArray.length !== 32) {
            console.log(`   ❌ ERROR: Invalid public key length! Expected 32 bytes, got ${signerArray.length}`);
          } else {
            console.log(`   ✅ Public key length is correct (32 bytes)`);
          }
        }
      }
      
      // Also check for ResourceAccount which might contain the resource_address
      if (resource.type.includes('ResourceAccount')) {
        console.log('   🔑 ResourceAccount found');
        console.log('   Data:', JSON.stringify(resource.data, null, 2));
      }
    }
  } catch (error: any) {
    console.error(`❌ Error listing resources: ${error.message}`);
  }
}

async function checkNetwork(network: Network) {
  console.log('\n' + '█'.repeat(80));
  console.log(`CHECKING NETWORK: ${network.toUpperCase()}`);
  console.log('█'.repeat(80));

  const config = new AptosConfig({ network });
  const aptos = new Aptos(config);

  // Check if contracts exist on this network
  try {
    await aptos.getAccountInfo({ accountAddress: FAILING_CONTRACT });
    console.log(`✅ Failing contract exists on ${network}`);
  } catch {
    console.log(`❌ Failing contract NOT found on ${network}`);
    return;
  }

  // List all resources for failing contract
  await listAllResources(aptos, FAILING_CONTRACT, 'FAILING CONTRACT');

  // List all resources for working contract
  await listAllResources(aptos, WORKING_CONTRACT, 'WORKING CONTRACT');
}

async function checkAdminState() {
  console.log('🔍 APTOS CONTRACT ADMINSTATE DIAGNOSTIC TOOL');
  console.log('='.repeat(80));
  console.log('This tool will:');
  console.log('1. Check MAINNET, TESTNET, and DEVNET');
  console.log('2. List all resources at both contract addresses');
  console.log('3. Find and validate AdminState resources');
  console.log('4. Identify the public key size issue');
  console.log('='.repeat(80));

  const networks = [Network.MAINNET, Network.TESTNET, Network.DEVNET];

  for (const network of networks) {
    await checkNetwork(network);
  }

  console.log('\n' + '█'.repeat(80));
  console.log('SUMMARY');
  console.log('█'.repeat(80));
  console.log('Look for AdminState resources above.');
  console.log('The failing contract should have an AdminState with an invalid');
  console.log('signer_u8_array (not 32 bytes).');
  console.log('\n💡 TIP: The AdminState might be stored at a different address');
  console.log('(resource_address) rather than the contract address itself.');
  console.log('Check for ResourceAccount resources that might indicate this.');
  console.log('='.repeat(80));
}

// Run the function
checkAdminState();