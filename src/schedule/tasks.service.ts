import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { CronSettings } from 'src/common/constants';
import { CommonService } from 'src/common-services/common.service';
import { Utils } from 'src/common/utils';
import { Article } from 'src/entities/article.entity';
import { ArticleStatus } from 'src/entities/enum.article.entity';
import { Quest, QuestStatus } from 'src/entities/quest.entity';
import { RedeemItem, RedeemItemStatus } from 'src/entities/redeem-item.entity';
import {
  Transaction,
  TransactionStatus,
  TransactionType,
} from 'src/entities/transaction.entity';
import { UserAsset, UserAssetStatus } from 'src/entities/user-asset.entity';
import { UserPointDaily } from 'src/entities/user-point-daily.entity';
import { FeedPricesService } from 'src/feed-prices/feed-prices.service';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class TasksService implements OnApplicationBootstrap {
  private readonly logger = new Logger(TasksService.name);

  constructor(
    private dataSource: DataSource,
    private readonly commonService: CommonService,
    private readonly feedPriceService: FeedPricesService,
    @InjectRepository(UserPointDaily)
    private userPointDailyRepository: Repository<UserPointDaily>,
  ) {}

  async onApplicationBootstrap() {
    await this.calculateDailyTransactionTotals();
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async syncPrice() {
    this.logger.debug('Sync price');
    await this.feedPriceService.syncPrice();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleVoucherExpiration() {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleVoucherOutOfStock() {}

  //Job to handle the expiration of quests
  //Job is operational at midnight every day, this can be adjusted as needed
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleQuestExpiration() {
    this.logger.debug('Checking expired quests...');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();

      await queryRunner.manager
        .createQueryBuilder()
        .update(Quest)
        .set({ status: QuestStatus.INACTIVE })
        .where('toDate < :now', { now })
        .andWhere('status = :status', { status: QuestStatus.ACTIVE })
        .execute();

      await queryRunner.commitTransaction();
      this.logger.log('[CRON] Updated expired quests successfully!');
    } catch (err) {
      await queryRunner.rollbackTransaction();
      this.logger.warn('[CRON] Failed to update expired quests');
    } finally {
      await queryRunner.release();
    }
  }

  //Job to handle the expiration of NFT vouchers
  //Job is operational at midnight every day, this can be adjusted as needed
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleNftVoucherExpiration() {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();

      await queryRunner.manager
        .createQueryBuilder()
        .update(UserAsset)
        .set({
          status: UserAssetStatus.EXPIRED,
          expiredAt: now,
        })
        .where('validTo < :now', { now })
        .andWhere('status != :expired', { expired: UserAssetStatus.EXPIRED })
        .andWhere('status != :used', { used: UserAssetStatus.USED })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .update(RedeemItem)
        .set({
          status: RedeemItemStatus.EXPIRED,
        })
        .where('validTo < :now', { now })
        .andWhere('status = :active', { active: RedeemItemStatus.ACTIVE })
        .execute();

      await queryRunner.commitTransaction();
      console.log(`[CRON] Updated expired vouchers successfully!`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('[CRON] Failed to update expired vouchers:', error);
    } finally {
      await queryRunner.release();
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleArticleExpiration() {
    this.logger.debug('Checking expired articles...');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();

      const result = await queryRunner.manager
        .createQueryBuilder()
        .update(Article)
        .set({ status: ArticleStatus.END })
        .where('endDate < :now', { now })
        .andWhere('status != :endStatus', { endStatus: ArticleStatus.END })
        .execute();

      if (result.affected && result.affected > 0) {
        this.logger.log(
          `[CRON] Updated ${result.affected} expired articles successfully!`,
        );
      }

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      this.logger.warn('[CRON] Failed to update expired articles');
    } finally {
      await queryRunner.release();
    }
  }

  // Job to calculate daily point for user
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  // @Cron(CronExpression.EVERY_5_SECONDS)
  async calculateDailyTransactionTotals() {
    await this.calculateDailyTransactionTotalsWithRetry(0);
  }

  private async calculateDailyTransactionTotalsWithRetry(attempt: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      const yesterday = new Date(
        now.getTime() - CronSettings.TIME_RANGE_HOURS * 60 * 60 * 1000,
      );

      const userResults = await queryRunner.manager
        .createQueryBuilder(Transaction, 'transaction')
        .innerJoin('transaction.userFrom', 'user')
        .select([
          'user.id as userId',
          'COALESCE(SUM(CASE WHEN transaction.type = :earnType THEN CAST(transaction.point AS DECIMAL(38,18)) ELSE 0 END), 0) AS totalEarn',
          'COALESCE(SUM(CASE WHEN transaction.type = :redeemType THEN CAST(transaction.point AS DECIMAL(38,18)) ELSE 0 END), 0) AS totalRedeem',
        ])
        .where('transaction.status = :status', {
          status: TransactionStatus.SUCCESS,
        })
        .andWhere('transaction.createdAt >= :yesterday', { yesterday })
        .andWhere('transaction.createdAt < :now', { now })
        .groupBy('user.id')
        .setParameter('earnType', TransactionType.EARN)
        .setParameter('redeemType', TransactionType.REDEEM)
        .getRawMany();

      if (userResults.length > 0) {
        const userPointDailyRecords = userResults.map((userResult) => {
          const totalEarn = parseFloat(userResult.totalEarn || '0');
          const totalRedeem = parseFloat(userResult.totalRedeem || '0');
          const totalPoints = totalEarn - totalRedeem;

          const userPointDaily = new UserPointDaily();
          userPointDaily.userId = userResult.userId;
          userPointDaily.dailyDate = now;
          userPointDaily.addedPoints = totalEarn.toString();
          userPointDaily.deductedPoints = totalRedeem.toString();
          userPointDaily.totalPoints = totalPoints.toString();

          return userPointDaily;
        });

        await queryRunner.manager.upsert(
          UserPointDaily,
          userPointDailyRecords,
          ['userId', 'dailyDate'],
        );
      }
      await queryRunner.commitTransaction();
      this.logger.log(
        '[CRON] Daily transaction totals calculation completed successfully!',
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (attempt < CronSettings.MAX_RETRIES_CALCULATE_POINT_DAILY - 1) {
        await queryRunner.release();

        // Wait 2 seconds before retry
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Retry
        await this.calculateDailyTransactionTotalsWithRetry(attempt + 1);
      } else {
        Utils.logMonitor(
          error,
          `[CRON] Failed to calculate daily transaction totals after ${CronSettings.MAX_RETRIES_CALCULATE_POINT_DAILY} attempts:`,
        );
        await queryRunner.release();
      }
    } finally {
      // Only release if not retrying
      if (attempt >= CronSettings.MAX_RETRIES_CALCULATE_POINT_DAILY - 1) {
        await queryRunner.release();
      }
    }
  }
}
