import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import {
  BANNED_DURATION,
  ErrorCode,
  LOGIN_ATTEMPTS,
  MAIL_DETAIL,
  OTP_LIFE_SPAN,
  REFERRAL_POINT,
} from 'src/common/constants';
import { Utils } from 'src/common/utils';
import { BonusHistory } from 'src/entities/bonus-history.entity';
import { UserBrand } from 'src/entities/user-branch.entity';
import { UserChain } from 'src/entities/user-chain.entity';
import { User, UserPayload, UserStatus } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { TwitterService } from 'src/twitter/twitter.service';
import { DataSource, Repository } from 'typeorm';
import { AddReferralCodeDto } from './dto/add-referral-code.dto';
import { CheckPincodeDto } from './dto/check-pincode.dto';
import { LinkTwitterDto } from './dto/link-twitter.dto';
import { LoginDto } from './dto/login.dto';
import { PreLoginDto } from './dto/pre-login';
import { ResendOtpDto } from './dto/resend-otp.dto';
import { ResetPinDto } from './dto/reset-pin.dto';
import { UpdatePinCodeDto } from './dto/update-pincode.dto';
import { VerifyDto } from './dto/verify.dto';
import { GenHashCodeDto } from '../brands/dto/gen-hash-code.dto';
import { Chain, ChainName } from 'src/entities/chain.entity';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private jwtService: JwtService,
    private commonService: CommonService,
    private queueService: QueueService,
    private twitterService: TwitterService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserBrand)
    private userBrandRepository: Repository<UserBrand>,
    @InjectRepository(UserChain)
    private userChainRepository: Repository<UserChain>,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    private readonly dataSource: DataSource,
  ) {}

  async preLogin(requestData: PreLoginDto, token: string): Promise<boolean> {
    try {
      const { email } = requestData;
      const payload = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET_MERCHANT,
      });
      const existingUser = await this.userRepository.findOneBy({
        email,
      });

      const otp = Utils.generateOTP();
      const expirationTime = new Date();
      expirationTime.setSeconds(expirationTime.getSeconds() + OTP_LIFE_SPAN);

      this.queueService.addMailToQueue({
        to: email,
        subject: MAIL_DETAIL.VERIFY_CREATE_WALLET.TITLE,
        template: MAIL_DETAIL.VERIFY_CREATE_WALLET.TEMPLATE,
        context: { otp: otp },
      });

      if (!existingUser) {
        const newRefCode = this.commonService.generateCodeRef();
        const randomNumber = Math.floor(Math.random() * 900000) + 100000;
        const newUsername = `user${randomNumber}`;

        const newUser = await this.userRepository.save(
          this.userRepository.create({
            email,
            otp,
            otpExpiryTime: expirationTime,
            refCode: newRefCode,
            name: newUsername,
          }),
        );

        let activeChainId = newUser.activeChainId;
        if (!activeChainId) {
          const aptopChains = await this.chainRepository.findOne({
            where: { chainName: ChainName.APTOS },
            select: ['id'],
          });
          if (aptopChains) {
            activeChainId = aptopChains.id;
            await this.userRepository.update(newUser.id, { activeChainId });
          }
        }

        await this.userBrandRepository.save(
          this.userBrandRepository.create({
            userId: newUser.id,
            brandId: payload.brandId,
          }),
        );
      } else {
        const [_, existingUserBrand] = await Promise.all([
          this.userRepository.update(existingUser.id, {
            otp,
            otpExpiryTime: expirationTime,
          }),
          this.userBrandRepository.findOneBy({
            userId: existingUser.id,
            brandId: payload.brandId,
          }),
        ]);

        if (!existingUserBrand) {
          await this.userBrandRepository.save(
            this.userBrandRepository.create({
              userId: existingUser.id,
              brandId: payload.brandId,
            }),
          );
        }
      }
      return true;
    } catch (error) {
      this.logger.error('Error in preLogin:', error);
      throw error;
    }
  }

  async login(requestData: LoginDto, token: string) {
    const { email, pinCode } = requestData;
    const decodedPayload = this.jwtService.verify(token, {
      secret: process.env.JWT_SECRET_MERCHANT,
    });
    const user = await this.userRepository.findOne({
      where: { email },
      select: {
        id: true,
        email: true,
        status: true,
        pinCode: true,
        twitterId: true,
        twitterUsername: true,
        loginAttempt: true,
        banExpiryTime: true,
        firstLogin: true,
        name: true,
      },
    });

    if (!user) {
      throw ApiError('', 'Wallet does not exist');
    }

    if (user.status === UserStatus.INACTIVE) {
      throw ApiError('', 'Wallet is inactive');
    }

    // Create wallet if not exist
    const nex3Chains = await this.commonService.getNex3Chains();

    if (nex3Chains.length > 0) {
      for (const chain of nex3Chains) {
        await this.commonService.createUserChain({
          userId: user.id,
          chain: chain,
        });
      }
    }

    let updateData: Partial<User> = {};

    if (user.status === UserStatus.BANNED) {
      const now = new Date();
      if (user.banExpiryTime && now > user.banExpiryTime) {
        updateData = {
          status: UserStatus.ACTIVE,
          banExpiryTime: null,
        };
      } else {
        const diffMs = user.banExpiryTime.getTime() - now.getTime();
        const remainingMinutes = Math.ceil(diffMs / (1000 * 60));

        throw ApiError('E8', `${remainingMinutes}`);
      }
    }

    if (!user.pinCode) {
      throw ApiError('', 'Wallet has no pincode');
    }

    const validPincode = await Utils.comparePincode(pinCode, user.pinCode);
    if (!validPincode) {
      let loginAttempt = user.loginAttempt ?? 0;
      loginAttempt++;

      if (loginAttempt > LOGIN_ATTEMPTS) {
        const banUntil = new Date();
        banUntil.setMinutes(banUntil.getMinutes() + BANNED_DURATION);

        await this.userRepository.update(user.id, {
          status: UserStatus.BANNED,
          banExpiryTime: banUntil,
          loginAttempt: 0,
        });

        throw ApiError(
          'E7',
          `You have entered the wrong PIN too many times. For security reasons, your account has been locked for 15 minutes. Please try again later.`,
        );
      } else {
        await this.userRepository.update(user.id, { loginAttempt });
      }

      throw ApiError('E6', 'PIN is not correct');
    }

    updateData.loginAttempt = 0;

    let isFirstLogin = false;
    if (user.firstLogin) {
      isFirstLogin = true;
      updateData.firstLogin = false;
    }

    const userBrandPromise = this.userBrandRepository.findOneBy({
      brandId: decodedPayload.brandId,
      userId: user.id,
    });

    const shouldNotUpdate =
      user.status === UserStatus.ACTIVE &&
      user.loginAttempt === 0 &&
      !user.firstLogin;

    const updatePromise = !shouldNotUpdate
      ? this.userRepository.update(user.id, updateData)
      : Promise.resolve();

    const [userBrand] = await Promise.all([userBrandPromise, updatePromise]);

    const payload = {
      id: user.id,
      email: user.email,
      brandId: userBrand.brandId,
    };

    this.logger.log(`Wallet with email ${email} logged in successfully`);
    return {
      accessToken: this.jwtService.sign(payload),
      twitterId: user.twitterId,
      twitterUsername: user.twitterUsername,
      firstLogin: isFirstLogin,
    };
  }

  async verify(requestData: VerifyDto) {
    try {
      const { email, otp } = requestData;
      const user = await this.userRepository.findOne({
        where: { email },
        select: ['id', 'email', 'otp', 'otpExpiryTime', 'status'],
      });

      if (!user) {
        throw ApiError('', 'Wallet does not exist');
      }

      const now = new Date();
      if (!user.otpExpiryTime || user.otpExpiryTime < now) {
        throw ApiError('E4', 'OTP expired');
      }

      if (user.otp !== otp) {
        throw ApiError('E4', 'OTP invalid');
      }

      if (user.status === UserStatus.INACTIVE) {
        await this.userRepository.update(user.id, {
          status: UserStatus.ACTIVE,
        });
      }

      this.logger.log(`Wallet with email ${email} verified successfully`);
      return true;
    } catch (error) {
      this.logger.error('Error in verify:', error);
      throw error;
    }
  }

  async resendOtp(requestData: ResendOtpDto) {
    const { email } = requestData;
    const user = await this.userRepository.findOne({
      where: { email },
      select: ['id', 'email'],
    });

    if (!user) {
      throw ApiError('', 'Wallet does not exist');
    }

    const newOtp = Utils.generateOTP();
    const expirationTime = new Date();
    expirationTime.setSeconds(expirationTime.getSeconds() + OTP_LIFE_SPAN);

    await this.userRepository.update(user.id, {
      otp: newOtp,
      otpExpiryTime: expirationTime,
    });

    this.logger.log(`New OTP sent to ${email}: ${newOtp}`);
    this.queueService.addMailToQueue({
      to: email,
      subject: MAIL_DETAIL.VERIFY_CREATE_WALLET.TITLE,
      template: MAIL_DETAIL.VERIFY_CREATE_WALLET.TEMPLATE,
      context: { otp: newOtp },
    });

    return {
      otpExpiresAt: expirationTime,
    };
  }

  async forgotPin(requestData: ResendOtpDto) {
    const { email } = requestData;
    const user = await this.userRepository.findOne({
      where: { email },
      select: ['id', 'email'],
    });

    if (!user) {
      throw ApiError('', 'Wallet does not exist');
    }

    const newOtp = Utils.generateOTP();
    const expirationTime = new Date();
    expirationTime.setSeconds(expirationTime.getSeconds() + OTP_LIFE_SPAN);

    await this.userRepository.update(user.id, {
      otp: newOtp,
      otpExpiryTime: expirationTime,
    });

    this.queueService.addMailToQueue({
      to: email,
      subject: MAIL_DETAIL.RESET_WALLET_PIN.TITLE,
      template: MAIL_DETAIL.RESET_WALLET_PIN.TEMPLATE,
      context: { otp: newOtp },
    });

    return {
      otpExpiresAt: expirationTime,
    };
  }

  async resetPin(requestData: ResetPinDto) {
    const { email, pinCode } = requestData;
    const user = await this.userRepository.findOne({
      where: { email },
      select: ['id', 'email', 'status'],
    });

    if (!user) {
      throw ApiError('', 'Wallet does not exist');
    }

    if (user.status === UserStatus.INACTIVE) {
      throw ApiError('', 'Wallet is inactive');
    }

    const hashedPincode = await Utils.hashPincode(pinCode);
    await this.userRepository.update(user.id, { pinCode: hashedPincode });

    this.logger.log(`Pin code reset successfully for user with email ${email}`);
    return { message: 'Pin code has been reset successfully' };
  }

  async updatePinCode(updatePinCode: UpdatePinCodeDto) {
    try {
      const { email, pinCode } = updatePinCode;
      const user = await this.userRepository.findOne({
        where: { email },
        select: ['id', 'email', 'status'],
      });

      if (!user) {
        throw ApiError('', 'Wallet does not exist');
      }

      if (user.status === UserStatus.INACTIVE) {
        throw ApiError('', 'Wallet is inactive');
      }

      const hashedPincode = await Utils.hashPincode(pinCode);
      await this.userRepository.update(user.id, { pinCode: hashedPincode });

      this.logger.log(
        `Pin code updated successfully for user with email ${email}`,
      );
      return true;
    } catch (error) {
      this.logger.error('Error updating pin code:', error);
      throw error;
    }
  }

  async checkPinCode(checkPinCode: CheckPincodeDto) {
    try {
      const { email } = checkPinCode;
      const user = await this.userRepository.findOne({
        where: { email },
        select: ['id', 'email', 'pinCode'],
      });

      if (!user) {
        throw ApiError('E17', 'Email does not exist in the system');
      }

      this.logger.log(`Check Pincode successfully`);

      if (user.pinCode) {
        return { hasPincode: true };
      } else {
        throw ApiError('', 'Wallet has no pincode');
      }
    } catch (error) {
      this.logger.error('Error checking pin code:', error);
      throw error;
    }
  }

  async addReferralCode(requestData: AddReferralCodeDto) {
    const { email, refCode } = requestData;

    try {
      const user = await this.userRepository.findOne({
        where: { email },
        select: {
          id: true,
          refCode: true,
          referredByUserId: true,
        },
      });

      if (!user) {
        throw ApiError('', 'Wallet does not exist');
      }

      if (user.referredByUserId) {
        throw ApiError('', 'User already has a referrer');
      }

      let parentUser: User;

      if (refCode && user?.refCode !== refCode && !user?.referredByUserId) {
        parentUser = await this.userRepository.findOne({
          where: { refCode },
          select: {
            id: true,
          },
        });
      }

      if (!parentUser) {
        throw ApiError('E22', 'Referral code is invalid');
      }

      await Promise.all([
        this.userRepository.update(user.id, {
          referredByUserId: parentUser ? parentUser.id : null,
        }),
        this.dataSource.manager.increment(
          User,
          { id: parentUser.id },
          'refereeCount',
          1,
        ),
        this.dataSource.manager.increment(
          User,
          { id: parentUser.id },
          'point',
          REFERRAL_POINT,
        ),
        this.dataSource.manager.increment(
          User,
          { id: user.id },
          'point',
          REFERRAL_POINT,
        ),
        this.dataSource.manager.insert(BonusHistory, {
          referrerId: parentUser.id,
          referredId: user.id,
          point: REFERRAL_POINT.toString(),
        }),
      ]);
      return true;
    } catch (error) {
      this.logger.error('Error adding referral code:', error);
      throw error;
    }
  }

  async linkTwitter(linkTwitter: LinkTwitterDto, currentUser: UserPayload) {
    const { token } = linkTwitter;
    const user = await this.userRepository.findOne({
      where: { email: currentUser.email },
      select: [
        'id',
        'email',
        'twitterId',
        'twitterUsername',
        'name',
        'avatarUrl',
      ],
    });

    try {
      const twitterUser = await this.twitterService.getCurrentUser(token);

      // Check linked account
      const count = await this.userRepository.countBy({
        twitterId: twitterUser.data.id,
        twitterUsername: twitterUser.data.username,
      });
      if (count > 0) {
        if (user.twitterId === twitterUser.id) {
          throw ApiError(
            ErrorCode.ALREADY_LINKED_ACCOUNT,
            `This twitter id ${twitterUser.data.username} already register`,
          );
        } else {
          throw ApiError(
            ErrorCode.ALREADY_LINKED_ANOTHER_ACCOUNT,
            `This twitter id ${twitterUser.data.username} already linked with another account`,
          );
        }
      } else {
        // Update db
        const res = await this.userRepository.update(
          { email: currentUser.email },
          {
            twitterId: twitterUser.data.id,
            twitterUsername: twitterUser.data.username,
          },
        );
      }
      return true;
    } catch (error) {
      this.logger.error('Link twitter error', error);
      return false;
    }
  }
}
