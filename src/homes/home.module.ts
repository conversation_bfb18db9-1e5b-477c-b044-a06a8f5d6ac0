import { Module } from '@nestjs/common';
import { HomeService } from './home.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/entities/user.entity';
import { Asset } from 'src/entities/asset.entity';
import { CommonModule } from 'src/common-services/common.module';
import { UserAsset } from 'src/entities/user-asset.entity';
import { HomeController } from './home.controller';
import { RedeemItem } from 'src/entities/redeem-item.entity';
import { Chain } from 'src/entities/chain.entity';

@Module({
  controllers: [HomeController],
  providers: [HomeService],
  imports: [
    TypeOrmModule.forFeature([User, Asset, RedeemItem, UserAsset, Chain]),
    CommonModule,
  ],
})
export class HomeModule {}
