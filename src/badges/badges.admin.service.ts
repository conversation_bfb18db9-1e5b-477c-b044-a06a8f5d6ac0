import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { AdminPayload } from 'src/entities/admin.entity';
import { Badge } from 'src/entities/badge.entity';
import { Repository } from 'typeorm';

@Injectable()
export class BadgesAdminService {
  private readonly logger = new Logger(BadgesAdminService.name);
  constructor(
    @InjectRepository(Badge)
    private badgeRepository: Repository<Badge>,
  ) {}

  async importBadgesCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];

      const badges: Partial<Badge>[] = rows.map((row) => {
        return {
          name: row['name'],
          image: row['image'],
          rank: row['rank'],
          description: row['description'],
        };
      });

      await this.badgeRepository.upsert(badges, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
