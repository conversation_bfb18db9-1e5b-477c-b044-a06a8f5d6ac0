import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BadgeRank } from 'src/entities/badge.entity';

export class BadgeResponseDto {
  @ApiProperty()
  @Expose({ name: 'badge_id' })
  id: number;

  @ApiProperty()
  @Expose({ name: 'badge_name' })
  name: string;

  @ApiProperty()
  @Expose({ name: 'badge_image' })
  image: string;

  @ApiProperty()
  @Expose({ name: 'badge_description' })
  description: string;

  @ApiProperty({ enum: BadgeRank })
  @Expose({ name: 'badge_rank' })
  rank: BadgeRank;

  @ApiProperty()
  @Expose({ name: 'badge_createdAt' })
  createdAt: Date;

  @ApiProperty()
  @Expose({ name: 'badge_updatedAt' })
  updatedAt: Date;
}
