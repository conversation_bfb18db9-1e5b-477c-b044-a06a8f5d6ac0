import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BadgeRank } from 'src/entities/badge.entity';

export class GetBadgeDetailResponseDto {
  @ApiProperty()
  @Expose()
  id: number;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  image: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty({ enum: BadgeRank })
  @Expose()
  rank: BadgeRank;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;
}
