import {
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { BadgesAdminService } from './badges.admin.service';
import { FileInterceptor } from '@nestjs/platform-express';

@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('admin/badges')
export class BadgesAdminController {
  constructor(private readonly badgesAdminService: BadgesAdminService) {}

  @Post('import-badges')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Import badges CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importBadgesCSV(@UploadedFile() file: Express.Multer.File) {
    return this.badgesAdminService.importBadgesCSV(file);
  }
}
