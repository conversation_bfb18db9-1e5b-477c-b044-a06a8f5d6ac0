import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Badge } from 'src/entities/badge.entity';
import { Repository } from 'typeorm';
import { PanigateBadgesDto } from './dto/panigate-badges.dto';
import { Utils } from 'src/common/utils';
import { plainToInstance } from 'class-transformer';
import { BadgeResponseDto } from './dto/badges-list.dto';
import { UserPayload } from 'src/entities/user.entity';
import { GetBadgeDetailResponseDto } from './dto/get-badges-detail.dto';

@Injectable()
export class BadgesService {
  constructor(
    @InjectRepository(Badge)
    private readonly badgeRepository: Repository<Badge>,
  ) {}

  async getAllBadge(currentUser: UserPayload, requestData: PanigateBadgesDto) {
    const queryBuilder = this.badgeRepository
      .createQueryBuilder('badge')
      .orderBy('badge.createdAt', 'DESC');

    queryBuilder.where('badge.isDeleted = false');

    const data = await Utils.paginate<any>(queryBuilder, requestData, true);

    return {
      ...data,
      items: plainToInstance(BadgeResponseDto, data.items, {
        excludeExtraneousValues: true,
      }),
    };
  }

  async getBadgeById(id: number): Promise<GetBadgeDetailResponseDto> {
    const badge = await this.badgeRepository
      .createQueryBuilder('badge')
      .where('badge.id = :id AND badge.isDeleted = false', { id })
      .getOne();

    if (!badge) {
      throw new NotFoundException(`Badge with ID ${id} not found`);
    }

    return plainToInstance(GetBadgeDetailResponseDto, badge, {
      excludeExtraneousValues: true,
    });
  }
}
