import { Modu<PERSON> } from '@nestjs/common';
import { BadgesController } from './badges.controller';
import { BadgesService } from './badges.service';
import { CategoryService } from 'src/category/category.service';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Badge } from 'src/entities/badge.entity';
import { User } from 'src/entities/user.entity';
import { CommonModule } from 'src/common-services/common.module';
import { BadgesAdminService } from './badges.admin.service';
import { BadgesAdminController } from './badges.admin.controller';

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET_MERCHANT,
      signOptions: { expiresIn: process.env.JWT_EXPIRATION_TIME },
    }),
    TypeOrmModule.forFeature([Badge, User]),
    CommonModule,
  ],
  controllers: [BadgesController, BadgesAdminController],
  providers: [BadgesService, BadgesAdminService],
  exports: [BadgesService],
})
export class BadgesModule {}
