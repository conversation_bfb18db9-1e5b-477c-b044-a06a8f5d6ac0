import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { BadgesService } from './badges.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { PanigateBadgesDto } from './dto/panigate-badges.dto';
import { Pagination } from 'nestjs-typeorm-paginate';
import { BadgeResponseDto } from './dto/badges-list.dto';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { GetBadgeDetailResponseDto } from './dto/get-badges-detail.dto';

@ApiBearerAuth()
@Controller('badges')
export class BadgesController {
  constructor(private readonly badgesService: BadgesService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get Badges ' })
  async getAllBadges(
    @User() currentUser: UserPayload,
    @Query() requestData: PanigateBadgesDto,
  ): Promise<Pagination<BadgeResponseDto>> {
    return this.badgesService.getAllBadge(currentUser, requestData);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get detail badges by id' })
  async findOneById(
    @Param('id') id: number,
  ): Promise<GetBadgeDetailResponseDto> {
    return this.badgesService.getBadgeById(id);
  }
}
