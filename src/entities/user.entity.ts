import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { Badge } from './badge.entity';
import { QuestTaskUser } from './quest-task-user.entity';
import TimestampEntity from './timestamp.entity';
import { UserAsset } from './user-asset.entity';
import { UserChain } from './user-chain.entity';
import { BonusHistory } from './bonus-history.entity';
import { IsEmail, IsInt, IsOptional } from 'class-validator';
import { Chain } from './chain.entity';

export class UserPayload {
  @IsInt()
  id: number;

  @IsEmail()
  email: string;

  @IsInt()
  brandId: number;
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

@Entity({ name: 'users' })
export class User extends TimestampEntity {
  @Column({ unique: true })
  email: string;

  @Column({ nullable: true })
  pinCode: string;

  @Column({ nullable: true })
  otp: string;

  @Column({ nullable: true })
  otpExpiryTime: Date;

  @Column({ nullable: true })
  loginAttempt: number;

  @Column({ nullable: true })
  banExpiryTime: Date;

  @Column({ type: 'enum', enum: UserStatus, default: UserStatus.INACTIVE })
  status: UserStatus;

  @OneToMany(() => QuestTaskUser, (questTaskUser) => questTaskUser.user)
  questTaskUsers: QuestTaskUser[];

  @OneToMany(() => UserChain, (userChain) => userChain.user)
  userChains: UserChain[];

  @OneToMany(() => UserAsset, (userAsset) => userAsset.user)
  userAssets: UserAsset[];

  @Column({ nullable: true })
  refCode: string;

  @Column({ nullable: true })
  referredByUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'referredByUserId' })
  referredByUser: User;

  @Column({ default: 0 })
  refereeCount: number;

  @Column({ nullable: true })
  twitterId: string;

  @Column({ nullable: true })
  twitterUsername: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  avatarUrl: string;

  @Column({ nullable: true })
  badge1Id: number;

  @ManyToOne((type) => Badge)
  @JoinColumn({ name: 'badge1Id' })
  badge1: Badge;

  @Column({ nullable: true })
  badge2Id: number;

  @ManyToOne((type) => Badge)
  @JoinColumn({ name: 'badge2Id' })
  badge2: Badge;

  @Column({ nullable: true })
  badge3Id: number;

  @ManyToOne((type) => Badge)
  @JoinColumn({ name: 'badge3Id' })
  badge3: Badge;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  point: string;

  @OneToMany(() => BonusHistory, (bonusHistory) => bonusHistory.referrer)
  bonusHistoriesAsReferrer: BonusHistory[];

  @Column({ type: 'boolean', default: true })
  firstLogin: boolean;

  @Column({ nullable: true })
  activeChainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'activeChainId' })
  activeChain: Chain;
}
