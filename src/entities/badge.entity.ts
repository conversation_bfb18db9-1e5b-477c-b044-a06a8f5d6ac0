import { Column, Entity, OneToOne } from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { UserBadge } from './user-badge.entity';

export enum BadgeId {
  X_BEGINNER = 1,
  DISCORD_BEGINNER = 2,
  PATH_FINDER = 3,
  ROOKIE = 4,
}

export enum BadgeRank {
  BRONZE_TIER_1 = 'Bronze (Tier 1)',
  BRONZE_TIER_2 = 'Bronze (Tier 2)',
  BRONZE_TIER_3 = 'Bronze (Tier 3)',
  OTHERS = 'Others',
}

@Entity({ name: 'badges' })
export class Badge extends TimestampEntity {
  @OneToOne((type) => UserBadge, (type) => type.badge)
  userBadge: UserBadge;

  @Column()
  name: string;

  @Column()
  image: string;

  @Column({
    type: 'enum',
    enum: BadgeRank,
    default: BadgeRank.OTHERS,
  })
  rank: BadgeRank;

  @Column()
  description: string;
}
