import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Airdrop } from './airdrop.entity';
import TimestampEntity from './timestamp.entity';

export enum AirdropPhaseStatus {
  COMING_SOON = 'coming-soon',
  ACTIVE = 'active',
  END = 'end',
}

@Entity({ name: 'airdrop_phases' })
export class AirdropPhase extends TimestampEntity {
  @Column({ nullable: true })
  airdropId: number;

  @ManyToOne(() => Airdrop)
  @JoinColumn({ name: 'airdropId' })
  airdrop: Airdrop;

  @Column({ type: 'tinyint', unsigned: true, default: 1 })
  phaseOrder: number;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  milestonePoint: string;

  @Column({ type: 'timestamp' })
  startDate: Date;

  @Column({ type: 'timestamp' })
  endDate: Date;

  @Column({
    type: 'enum',
    enum: AirdropPhaseStatus,
    default: AirdropPhaseStatus.COMING_SOON,
  })
  status: AirdropPhaseStatus;
}
