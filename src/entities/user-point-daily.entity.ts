import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, Unique } from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { User } from './user.entity';

@Entity({ name: 'user_point_daily' })
@Unique(['userId', 'dailyDate'])
export class UserPointDaily extends TimestampEntity {
  @Column()
  userId: number;

  @Column({ type: 'date' })
  dailyDate: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 18,
    default: 0,
    unsigned: true,
  })
  addedPoints: string;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 18,
    default: 0,
    unsigned: false,
  })
  deductedPoints: string;

  @Column({
    type: 'decimal',
    precision: 38,
    scale: 18,
    default: 0,
    unsigned: false,
  })
  totalPoints: string;
}
