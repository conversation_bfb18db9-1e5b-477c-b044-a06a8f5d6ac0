import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { AirdropPhase } from './airdrop-phase.entity';
import { Asset } from './asset.entity';
import { Chain } from './chain.entity';
import TimestampEntity from './timestamp.entity';
import { Sponsor } from './sponsor.entity';

export enum AirdropStatus {
  COMING_SOON = 'coming-soon',
  ACTIVE = 'active',
  END = 'end',
}

@Entity({ name: 'airdrops' })
export class Airdrop extends TimestampEntity {
  @OneToMany(() => AirdropPhase, (airdropPhase) => airdropPhase.airdrop)
  airdropPhases: AirdropPhase[];

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  chainId: number;

  @ManyToOne(() => Chain)
  @JoinColumn({ name: 'chainId' })
  chain: Chain;

  @Column({ nullable: true })
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ nullable: true })
  assetId: number;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  rewardAmount: string;

  @Column()
  rewardContractAddress: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  convertedPoints: string;

  @Column({ type: 'timestamp' })
  startDate: Date;

  @Column({ type: 'timestamp' })
  endDate: Date;

  @Column({
    type: 'enum',
    enum: AirdropStatus,
    default: AirdropStatus.COMING_SOON,
  })
  status: AirdropStatus;
}
