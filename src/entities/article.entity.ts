import {
  BaseEntity,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Brand } from './brand.entity';
import { SponsorAsset } from './sponsor-asset.entity';
import TimestampEntity from './timestamp.entity';
import { ArticleQuestion } from './article-question.entity';
import { UserArticleAnswer } from './user-article-answer.entity';
import { Sponsor } from './sponsor.entity';
import { Category } from './category.entity';
import { UserQuest } from './user-quest.entity';
import { Type } from './type.entity';
import { ArticleCategory, ArticleStatus } from './enum.article.entity';

@Entity({ name: 'articles' })
export class Article extends TimestampEntity {
  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  point: string;

  @Column({ type: 'decimal', precision: 38, scale: 18, default: 0 })
  claimedPoint: string;

  @Column()
  title: string;

  @Column({
    type: 'enum',
    enum: ArticleCategory,
    default: ArticleCategory.GENERAL,
  })
  category: ArticleCategory;

  @Column()
  imageUrl: string;

  @Column({ type: 'text' })
  content: string;

  @Column()
  readTime: string;

  @Column({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  endDate: Date;

  @OneToMany(
    () => ArticleQuestion,
    (articleQuestion) => articleQuestion.article,
  )
  questions: ArticleQuestion[];

  @OneToMany(
    () => UserArticleAnswer,
    (userArticleAnswer) => userArticleAnswer.article,
  )
  userArticleAnswers: UserArticleAnswer[];

  @Column({
    type: 'enum',
    enum: ArticleStatus,
    default: ArticleStatus.INACTIVE,
  })
  status: ArticleStatus;

  @Column()
  brandId: number;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brandId' })
  brand: Brand;

  @Column({ default: 0 })
  sponsorId: number;

  @ManyToOne(() => Sponsor)
  @JoinColumn({ name: 'sponsorId' })
  sponsor: Sponsor;

  @Column({ nullable: true })
  categoryId: number;

  @ManyToOne(() => Category)
  @JoinColumn({ name: 'categoryId' })
  categories: Category;

  @ManyToMany(() => Type, { eager: true })
  @JoinTable({
    name: 'article_types',
    joinColumn: { name: 'articleId' },
    inverseJoinColumn: { name: 'typeId' },
  })
  types: Type[];
}
