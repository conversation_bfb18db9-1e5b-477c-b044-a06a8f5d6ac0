import {
  BaseEntity,
  Column,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import TimestampEntity from './timestamp.entity';
import { HotWallet } from './hot-wallet.entity';
import { User } from './user.entity';

export enum ChainName {
  ETHEREUM = 'ethereum',
  BSC = 'bsc',
  BASE = 'base',
  APTOS = 'aptos',
}

export enum ChainType {
  EVM = 'evm',
  MOVEVM = 'movevm',
}

@Entity({ name: 'chains' })
export class Chain extends TimestampEntity {
  @Column({ unique: true })
  chainName: string;

  @Column({ type: 'enum', enum: ChainType })
  chainType: ChainType;

  @Column({ nullable: true })
  imageUrl: string;

  @Column()
  blockchainId: string;

  @Column({ type: 'mediumtext' })
  rpcs: string;

  @Column({ nullable: true })
  signer: string;

  @Column({ nullable: true })
  privateKey: string;

  @OneToMany(() => HotWallet, (hotwallet) => hotwallet.chain)
  hotwallets: HotWallet[];

  @Column({ nullable: true })
  nonceTrackerContractAddress: string;

  // Contract generated sponsor contract address
  @Column({ nullable: true })
  factorySponsorContractAddress: string;

  @Column({ nullable: true })
  voucherContractAddress: string;

  @Column({ nullable: true })
  gasSponsorContractAddress: string;

  @OneToMany(() => User, (user) => user.activeChain)
  users: User[];
}
