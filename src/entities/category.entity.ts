import { Column, Entity, OneToMany } from 'typeorm';
import { Article } from './article.entity';
import TimestampEntity from './timestamp.entity';

@Entity({ name: 'categories' })
export class Category extends TimestampEntity {
  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column()
  logoUrl: string;

  @Column({ nullable: true })
  contractAddress: string;

  @OneToMany(() => Article, (article) => article.categories)
  articleBrands: Article[];
}
