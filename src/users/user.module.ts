import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwsModule } from 'src/aws/aws.module';
import { CommonModule } from 'src/common-services/common.module';
import { Transaction } from 'src/entities/transaction.entity';
import { User<PERSON>hain } from 'src/entities/user-chain.entity';
import { User } from 'src/entities/user.entity';
import { QueueModule } from 'src/queue/queue.module';
import { UserAdminController } from './user.admin.controller';
import { UserAdminService } from './user.admin.service';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { Chain } from 'src/entities/chain.entity';

@Module({
  controllers: [UserAdminController, UserController],
  providers: [UserAdminService, UserService],
  imports: [
    CommonModule,
    QueueModule,
    AwsModule,
    TypeOrmModule.forFeature([User, UserChain, Transaction, Chain]),
  ],
  exports: [UserService],
})
export class UserModule {}
