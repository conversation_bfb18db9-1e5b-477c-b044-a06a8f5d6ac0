import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AwsService } from 'src/aws/aws.service';
import { ApiError } from 'src/common/api';
import { User, UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { Chain } from 'src/entities/chain.entity';
// import { UpdateProfileDto } from './dto/user/update-profile.dto';
import { UpdateActiveChainDto } from './dto/user/update-active-chain.dto';
import { UpdateProfileDto } from './dto/user/update-profile.dto';
// import { UpdateProfileDto } from './user/update-profile.dto';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Chain)
    private chainRepository: Repository<Chain>,
    private awsService: AwsService,
  ) {}

  async referralHistory(currentUser: UserPayload) {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoin('user.bonusHistoriesAsReferrer', 'bonusHistories')
      .leftJoin('bonusHistories.referred', 'referred')
      .leftJoin('user.referredByUser', 'referredByUser')
      .select([
        'user.id',
        'user.refCode',
        'referredByUser.id',
        'referredByUser.email',
        'referredByUser.refCode',
        'bonusHistories.id',
        'bonusHistories.point',
        'referred.id',
        'referred.email',
      ])
      .where('user.id = :id', { id: currentUser.id })
      .orderBy('referred.createdAt', 'DESC')
      .getOne();

    return user;
  }

  async uploadAvatar(currentUser: UserPayload, file: Express.Multer.File) {
    const user = await this.userRepository.findOne({
      where: { id: currentUser.id },
    });

    if (!user) {
      throw ApiError('', 'User not found');
    }

    const now = new Date();
    const key = `${user.id}/avatar_${now.getTime()}.${file.mimetype.split('/')[1]}`;

    const avatarUrl = await this.awsService.uploadImage(
      key,
      file.buffer,
      file.mimetype,
    );

    return { avatarUrl };
  }

  async updateProfile(
    currentUser: UserPayload,
    updateProfileDto: UpdateProfileDto,
  ) {
    const fieldsToUpdate: Partial<User> = {};
    if (updateProfileDto.avatarUrl) {
      fieldsToUpdate.avatarUrl = updateProfileDto.avatarUrl;
    }
    if (updateProfileDto.username) {
      fieldsToUpdate.name = updateProfileDto.username;
    }

    await this.userRepository.update({ id: currentUser.id }, fieldsToUpdate);

    return this.userRepository.findOneBy({ id: currentUser.id });
  }

  async updateActiveChain(
    currentUser: UserPayload,
    requestData: UpdateActiveChainDto,
  ) {
    if (!requestData.chainId) {
      await this.userRepository.update(
        { id: currentUser.id },
        { activeChainId: null },
      );
      return { message: 'Active chain reset successfully' };
    }
    const chain = await this.chainRepository.count({
      where: { id: requestData.chainId },
    });

    if (!chain) {
      throw new NotFoundException('Chain not found');
    }

    await this.userRepository.update(
      { id: currentUser.id },
      { activeChainId: requestData.chainId },
    );

    return { message: 'Active chain updated successfully' };
  }
}
