import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Put,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { UserService } from './user.service';
import { UpdateProfileDto } from './dto/user/update-profile.dto';
import { UpdateActiveChainDto } from './dto/user/update-active-chain.dto';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get referral history' })
  @HttpCode(HttpStatus.OK)
  @Get('referral-history')
  async referralHistory(@User() currentUser: UserPayload) {
    const result = await this.userService.referralHistory(currentUser);
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Upload avatar' })
  @HttpCode(HttpStatus.OK)
  @Post('upload-avatar')
  @UseInterceptors(FileInterceptor('file'))
  async uploadAvatar(
    @User() currentUser: UserPayload,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const result = await this.userService.uploadAvatar(currentUser, file);
    return result;
  }

  @Put('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update current user profile' })
  async updateProfile(
    @User() currentUser: UserPayload,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return this.userService.updateProfile(currentUser, updateProfileDto);
  }

  @Put('active-chain')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update active chain' })
  async updateActiveChain(
    @User() currentUser: UserPayload,
    @Body() requestData: UpdateActiveChainDto,
  ) {
    return this.userService.updateActiveChain(currentUser, requestData);
  }
}
