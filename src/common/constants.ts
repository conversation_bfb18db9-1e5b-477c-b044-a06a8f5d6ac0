export const ErrorCode = {
  INVALID_DATA: 'E900',
  NO_DATA_EXISTS: 'E901',
  SOMETHING_WENT_WRONG: 'E902',

  ALREADY_LINKED_ACCOUNT: 'E4',
  ALREADY_LINKED_ANOTHER_ACCOUNT: 'E5',

  // Twitter
  LINK_NOT_FOUND: 'E99',
  UNABLE_TO_COMPLETE_TASK: 'E100',
};

export const INTERNAL_SERVER_MESSAGE = 'Internal Server Error';

export const QueueSetting = {
  KEY_PREFIX: process.env.REDIS_PREFIX,
  OPTIONS: {
    backoff: 5 * 1000, // 5 seconds
    removeOnComplete: true,
    removeOnFail: true,
  },
  WEBHOOK_QUEUE: 'webhook-queue',
  MAIL_QUEUE: 'mail-queue',
  TRANSACTION_QUEUE: 'transaction-queue',
};

export const CacheSetting = {
  TTL: 600,
  SQL_TTL: 15 * 60 * 1000,
};

export const MimeType = {
  APPLICATION_JSON: 'application/json',
  IMAGE_PNG: 'image/png',
};

export const Language = {
  VN: 'vn',
};

export const MailConfig = {
  REPLY_TO: '<EMAIL>',
  FROM_MAIL: '<EMAIL>',
  DEFAULT_EMAIL_CSS: `body {
    font-family: Poppins;
    font-style: normal;
    margin: 0;
  }
  .container {
    width: 600px;
    margin: 0 auto;
    text-align: center;
    padding: 24px 0 16px;
  }     
  .content {
    text-align: left;
    padding: 42px 50px 24px;
    margin : 32px 0 54px;
    background: #FFFFFF;

    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: -0.003em;
    color: #4F5566;
  }
  .content .title {
    font-weight: 600;
    font-size: 32px;
    line-height: 48px;
    color: #23262F;
    margin: 0 0 14px;
  }
  .content p {
    font-weight: normal;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: -0.003em;
    color: #4F5566;
  }

  .content_code {
    margin: 0 auto 16px;
    width: fit-content;
    padding: 12px 64px;
    border: 1px solid #2e303e;
    border-radius: 20px;
    font-weight: 600;
    font-size: 36px;
    line-height: 54px;
    letter-spacing: 0.1em;
    color: #2d27ff;
    margin-bottom: 16px;
  }


  .social {
    padding-bottom: 20px;
    border-bottom: 1px solid #E6E8EC;
    width: 500px;
    margin: 0 auto;
  }
  .social img {
    width: 30px;
    height: 30px;
    margin: 0 10px;
  }

  button {
    cursor: pointer;
    border: none;
    border-radius: 8px;
    background: var(--brand-colors-logo, linear-gradient(135deg, #FF0A6C 0%, #2D27FF 100%));
  }

  button a {
    text-decoration: unset;
    display: inline-flex;
    padding: 6px 10px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: #FFF !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
  }

  .button-confirm {
    padding: 10px 14px;
    background: linear-gradient(135deg, #FF0A6C -16.8%, #2D27FF 138.64%);
    border: none;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    line-height: 21px;
    color: #FFFFFF !important;
  }
  .footer {
    color: #4F5566;
  }
  .footer-content {
    font-size: 16px;
    line-height: 26px;
  }
  .footer-copyright {
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
  }
  @media screen and (max-width: 565px) {
    .container {
      width: 100%;
    }
    .content {
      padding: 42px 16px 24px;
    }
    .content .title {
      font-size: 24px;
      line-height: 36px;
    }
    .social {
      width: 320px;
    }
  }`,
};

export const MAIL_DETAIL = {
  VERIFY_CREATE_WALLET: {
    TEMPLATE: 'M1',
    TITLE: 'NEX3 Verification code',
  },
  RESET_WALLET_PIN: {
    TEMPLATE: 'M2',
    TITLE: 'NEX3 Reset wallet PIN',
  },

  RESET_PASSWORD: {
    TEMPLATE: 'M3',
    TITLE: 'NEX3 Reset password for admin',
  },
};

export const OTP_LIFE_SPAN = 1800;
export const LOGIN_ATTEMPTS = 5;
export const BANNED_DURATION = 15;
export const REFERRAL_POINT = 1;

export const ContractError = {
  DUPLICATED_TRANSACTION_ID: 'DuplicatedTransactionID',
  ARTICLE_ALREADY_CLAIMED: 'ArticleAlreadyClaimed',
  ARTICLE_NOT_CLAIMED: 'ArticleNotClaimed',
};

export const ContractEvent = {
  NFT_VOUCHER_REDEEMED: 'NFTVoucherRedeemed',
  REWARD_CLAIMED: 'RewardClaimed',
  NFT_VOUCHER_SENT: 'NFTVoucherSent',
  TOKEN_SENT: 'TokenSent',
  NFT_REWARD_CLAIMED: 'NFTRewardClaimed',
  COLLECTION_CREATED: 'CollectionCreated',
};

export const SqlCacheKey = {
  CONFIG_GET: 'config:get',
  CONFIG_GET_FULL: 'config:get:full',
  CHAIN_GET: 'chain:get',
  CHAIN_GET_BY_CHAIN_TYPE: 'chain:get:by_chain_type',
  CHAIN_GET_BY_ID: 'chain:get:by_id',
  CHAIN_GET_BY_ASSET_ID: 'chain:get:by_asset_id',
  CHAIN_GET_BY_SPONSOR_ASSET_ID: 'chain:get:by_sponsor_asset_id',
  NEX3_CHAIN_GET: 'nex3:chain:get',
  NEX3_TOKEN_GET: 'nex3:token:get',
  SPONSOR_ASSET_GET_BY_ID: 'sponsor_asset:get:by_id',
  BRAND_GET_BY_ID: 'brand:get:by_id',
  HOT_WALLET_GET: 'hot_wallet:get',
  HOT_WALLET_GET_BY_ADDRESS: 'hot_wallet:get:by_address',
  USER_WALLET_GET: 'user_wallet:get',
  ASSET_GET_BY_ID: 'asset:get:by_id',
  REDEEM_ITEM_GET: 'redeem_item:get',
};

export const MIN_BALANCE = '0.000000000000000001';

export const CronSettings = {
  MAX_RETRIES_CALCULATE_POINT_DAILY: 3,
  TIME_RANGE_HOURS: 24, //hour
};

export const getContractMethods = (contractModule) => ({
  GRANT_NFT_FOR_USER: `${contractModule}::sponsor_factory::grant_NFT_for_user`,
  GRANT_TOKEN_FOR_USER: `${contractModule}::sponsor_factory::grant_token_for_user`,
  MINT_TOKEN: `${contractModule}::voucher::mint_token`,
  TRANSFER_TOKEN: `${contractModule}::aptos_account::transfer_fungible_assets`,
  TRANSFER_NFT: `${contractModule}::object::transfer`,
});
