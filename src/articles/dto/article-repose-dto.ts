import { ApiProperty } from '@nestjs/swagger';
import {
  Expose,
  Transform,
  Type as ClassTransformerType,
} from 'class-transformer';
// import { ArticleStatus, ArticleType } from 'src/entities/article.entity';
import { Brand } from 'src/entities/brand.entity';
import { Category } from 'src/entities/category.entity';
import { ArticleStatus, ArticleType } from 'src/entities/enum.article.entity';
import { Type } from 'src/entities/type.entity';
import { UserArticleAnswer } from 'src/entities/user-article-answer.entity';

export class ArticleResponseDto {
  @ApiProperty()
  @Expose({ name: 'article_id' })
  articleId: number;

  @ApiProperty()
  @Expose({ name: 'article_createdAt' })
  createdAt: Date;

  @ApiProperty()
  @Expose({ name: 'article_updatedAt' })
  updatedAt: Date;

  @ApiProperty()
  @Expose({ name: 'article_point' })
  articlePoint: string;

  @ApiProperty()
  @Expose({ name: 'article_title' })
  articleTitle: string;

  @ApiProperty({ enum: ArticleStatus })
  @Expose({ name: 'article_category' })
  articleCategory: ArticleStatus;

  @ApiProperty()
  @Expose({ name: 'article_imageUrl' })
  articleImageUrl: string;

  @ApiProperty()
  @Expose({ name: 'article_readTime' })
  articleReadTime: string;

  @ApiProperty()
  @Expose({ name: 'article_endDate' })
  endDate: Date;

  @ApiProperty({ type: () => Type, isArray: true })
  @Expose()
  @Transform(({ obj }) => {
    if (!obj.types_raw) return [];
    return obj.types_raw.split(',').map((item: string) => {
      const [id, name] = item.split(':');
      return { id: Number(id), name };
    });
  })
  types: Type[];

  @ApiProperty({ enum: ArticleStatus })
  @Expose({ name: 'article_status' })
  status: ArticleStatus;

  @ApiProperty({ type: () => Brand, required: false })
  @Expose()
  @Transform(({ obj }) => {
    const prefix = 'brand_';
    if (!obj[`${prefix}id`]) {
      return null;
    }

    const brand = {};
    for (const key in obj) {
      if (key.startsWith(prefix)) {
        brand[key.substring(prefix.length)] = obj[key];
      }
    }
    return brand;
  })
  Brand?: Brand;

  @ApiProperty({ type: () => UserArticleAnswer, required: false })
  @Expose()
  @Transform(({ obj }) => {
    const prefix = 'userArticleAnswers_';
    if (!obj[`${prefix}id`]) {
      return null;
    }

    const userArticleAnswers = {};
    for (const key in obj) {
      if (key.startsWith(prefix)) {
        userArticleAnswers[key.substring(prefix.length)] = obj[key];
      }
    }
    return userArticleAnswers;
  })
  userArticleAnswers?: UserArticleAnswer;

  @ApiProperty()
  @Expose()
  questionCount: number;
}
