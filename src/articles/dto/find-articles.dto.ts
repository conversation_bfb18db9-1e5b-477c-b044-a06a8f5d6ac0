import { SearchDto } from 'src/common/dto.common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsDateString, Validate, IsEnum } from 'class-validator';
import { QuestStatusByDate } from 'src/entities/quest.entity';
import { EnumQueryValidate } from 'src/common/validate/enum-query.validate';
import { UserQuestStatus } from 'src/entities/user-quest.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { Transform } from 'class-transformer';
import { ArticleStatus, ArticleType } from 'src/entities/enum.article.entity';

export class FindArticlesDto extends SearchDto {
  @ApiPropertyOptional({ type: String, format: 'date-time' })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  categoryId?: number;

  @ApiPropertyOptional({ type: String, format: 'date-time' })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({})
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? value.split(',') : value,
  )
  @IsEnum(ArticleStatus, {
    each: true,
    message: 'Each state must be a valid value (active, inactive, finished)',
  })
  statuses?: ArticleStatus[];

  @ApiProperty({
    enum: UserArticleAnswerStatus,
    required: false,
  })
  @IsOptional()
  @Validate(
    EnumQueryValidate,
    [UserArticleAnswerStatus.CLAIMED, UserArticleAnswerStatus.UNCLAIMED],
    {
      message: 'userClaimStatus must be a valid enum value',
    },
  )
  userClaimStatus: string;

  @ApiProperty({
    enum: ArticleType,
    required: false,
  })
  @IsOptional()
  @Validate(
    EnumQueryValidate,
    [ArticleType.APPLICATION, ArticleType.ASSET, ArticleType.TECHNOLOGY],
    {
      message: 'level must be a valid enum value',
    },
  )
  articleType: string;

  @ApiPropertyOptional({
    description: 'Filter by article type',
    enum: ArticleType,
    isArray: true,
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? value.split(',') : value,
  )
  @IsEnum(ArticleType, { each: true })
  types?: ArticleType[];
}
