import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { Type } from 'src/entities/type.entity';

export class ArticleDetailDto {
  @ApiProperty()
  @Expose({ name: 'article_id' })
  articleId: number;

  @ApiProperty()
  @Expose({ name: 'article_title' })
  articleTitle: string;

  @ApiProperty()
  @Expose({ name: 'article_content' })
  articleContent: string;

  @ApiProperty()
  @Expose({ name: 'article_read_time' })
  articleReadTime: string;

  @ApiProperty()
  @Expose({ name: 'article_point' })
  articlePoint: string;

  @ApiProperty()
  @Expose({ name: 'article_category' })
  articleCategory: string;

  @ApiProperty()
  @Expose({ name: 'article_status' })
  articleStatus: string;

  @ApiProperty()
  @Expose({ name: 'article_image_url' })
  articleImageUrl: string;

  @ApiProperty()
  @Expose({ name: 'sponsor_id' })
  sponsorId: number;

  @ApiProperty()
  @Expose({ name: 'sponsor_name' })
  sponsorName: string;

  @ApiProperty()
  @Expose({ name: 'sponsor_image_url' })
  sponsorImageUrl: string;

  @ApiProperty()
  @Expose({ name: 'user_attempt_status' })
  userAttemptStatus: string;

  @ApiProperty({ type: () => Type, isArray: true })
  @Expose()
  @Transform(({ obj }) => {
    if (!obj.types_raw) return [];
    return obj.types_raw.split(',').map((item: string) => {
      const [id, name] = item.split(':');
      return { id: Number(id), name };
    });
  })
  types: Type[];
}
