import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Article } from 'src/entities/article.entity';
import { DataSource, Repository, Brackets } from 'typeorm';
import {
  ArticleQuestion,
  ArticleQuestionType,
} from 'src/entities/article-question.entity';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { CheckAnswersDto } from './dto/check-answers.dto';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { User, UserPayload } from 'src/entities/user.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { AssetType } from 'src/entities/asset.entity';
import { ApiError } from 'src/common/api';
import { QuestionAnswerMap } from 'src/common/type.common';
import {
  Transaction,
  TransactionStatus,
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import { QueueService } from 'src/queue/queue.service';
import { CommonService } from 'src/common-services/common.service';
import { Sponsor } from 'src/entities/sponsor.entity';
import { SponsorAsset } from 'src/entities/sponsor-asset.entity';
import { Utils } from 'src/common/utils';
import { TransactionService } from 'src/transactions/transaction.service';
import { CreateTransactionDto } from 'src/transactions/dto/create-transaction.dto';

@Injectable()
export class ArticleAdminService {
  private readonly logger = new Logger(ArticleAdminService.name);

  constructor(
    private dataSource: DataSource,
    private queueService: QueueService,
    private commonService: CommonService,
    private transactionService: TransactionService,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(ArticleQuestion)
    private articleQuestionRepository: Repository<ArticleQuestion>,
    @InjectRepository(ArticleQuestionAnswer)
    private articleQuestionAnswerRepository: Repository<ArticleQuestionAnswer>,
  ) {}

  async importArticlesCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const articles: Partial<Article>[] = rows.map((row) => {
        return {
          title: row['title'],
          category: row['category'],
          imageUrl: row['imageUrl'],
          point: row['point'],
          content: row['content'],
          readTime: row['readTime'],
          status: row['status'],
          brandId: +row['brandId'],
          sponsorId: +row['sponsorId'],
          categoryId: +row['categoryId'],
        };
      });

      await this.articleRepository.upsert(articles, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });
      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async importArticlesQuestionsCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const articlesQuestions: Partial<ArticleQuestion>[] = rows.map((row) => {
        return {
          articleId: row['articleId'],
          question: row['question'],
          type: row['type'] as ArticleQuestionType,
          orderIndex: +row['orderIndex'],
        };
      });

      await this.articleQuestionRepository.upsert(articlesQuestions, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });
      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async importArticlesQuestionsAnswersCSV(file: Express.Multer.File) {
    try {
      const rows = (await Utils.readCsv(file)) as any[];
      const articlesQuestionsAnswers: Partial<ArticleQuestionAnswer>[] =
        rows.map((row) => {
          return {
            articleQuestionId: row['articleQuestionId'],
            answer: row['answer'],
            isCorrect: row['isCorrect'],
            orderIndex: +row['orderIndex'],
          };
        });

      await this.articleQuestionAnswerRepository.upsert(
        articlesQuestionsAnswers,
        {
          conflictPaths: ['id'],
          skipUpdateIfNoValuesChanged: true,
        },
      );
      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
