import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CommonService } from 'src/common-services/common.service';
import { ApiError } from 'src/common/api';
import { QuestionAnswerMap } from 'src/common/type.common';
import { ArticleQuestionAnswer } from 'src/entities/article-question-answer.entity';
import { ArticleQuestion } from 'src/entities/article-question.entity';
import { Article } from 'src/entities/article.entity';
import { Sponsor } from 'src/entities/sponsor.entity';
import {
  TransactionSubType,
  TransactionType,
} from 'src/entities/transaction.entity';
import {
  UserArticleAnswer,
  UserArticleAnswerStatus,
} from 'src/entities/user-article-answer.entity';
import { UserAsset } from 'src/entities/user-asset.entity';
import { UserPayload } from 'src/entities/user.entity';
import { QueueService } from 'src/queue/queue.service';
import { CreateTransactionDto } from 'src/transactions/dto/create-transaction.dto';
import { TransactionService } from 'src/transactions/transaction.service';
import { Brackets, DataSource, Repository } from 'typeorm';
import { CheckAnswersDto } from './dto/check-answers.dto';
import { Utils } from 'src/common/utils';
import { FindArticlesDto } from './dto/find-articles.dto';
import { QuestStatusByDate } from 'src/entities/quest.entity';
import { UserQuestStatus } from 'src/entities/user-quest.entity';
import { ArticleResponseDto } from './dto/article-repose-dto';
import { plainToInstance } from 'class-transformer';
import { ArticleStatus } from 'src/entities/enum.article.entity';
import { ArticleDetailDto } from './dto/article-detail.dto';

@Injectable()
export class ArticleService {
  private readonly logger = new Logger(ArticleService.name);

  constructor(
    private dataSource: DataSource,
    private queueService: QueueService,
    private commonService: CommonService,
    private transactionService: TransactionService,
    @InjectRepository(Article)
    private articleRepository: Repository<Article>,
    @InjectRepository(ArticleQuestion)
    private articleQuestionRepository: Repository<ArticleQuestion>,
    @InjectRepository(ArticleQuestionAnswer)
    private articleQuestionAnswerRepository: Repository<ArticleQuestionAnswer>,
    @InjectRepository(UserArticleAnswer)
    private userArticleAnswerRepository: Repository<UserArticleAnswer>,
    @InjectRepository(Sponsor)
    private sponsorRepository: Repository<Sponsor>,
    @InjectRepository(UserAsset)
    private userAssetRepository: Repository<UserAsset>,
  ) {}

  async getAll(currentUser: UserPayload, requestData: FindArticlesDto) {
    const {
      categoryId,
      userClaimStatus,
      articleType,
      keyword,
      statuses,
      types,
    } = requestData;
    let articles = this.articleRepository
      .createQueryBuilder('article')
      .leftJoin(
        'user-article-answers',
        'uaa',
        'uaa.articleId = article.id AND uaa.userId = :userId',
        { userId: currentUser.id },
      )
      .leftJoin('article.categories', 'categories')
      .leftJoinAndSelect('article.brand', 'brand')
      .leftJoinAndSelect(
        'article.userArticleAnswers',
        'userArticleAnswers',
        'userArticleAnswers.userId = :userId',
        { userId: currentUser.id },
      )
      .leftJoinAndSelect('article.types', 'types')
      .addSelect(
        `GROUP_CONCAT(DISTINCT CONCAT(types.id, ':', types.name)) AS types_raw`,
      )
      .groupBy('article.id')
      .orderBy('article.createdAt', 'DESC')
      .addOrderBy('article.title', 'ASC');

    if (keyword) {
      articles = Utils.applySearch(articles, keyword, [
        { name: 'article.title', isAbsolute: false },
        { name: 'brand.name', isAbsolute: false },
        { name: 'categories.name', isAbsolute: false },
        { name: 'categories.description', isAbsolute: false },
      ]);
    }

    if (types && types.length > 0) {
      articles.andWhere('types.name IN (:...types)', { types });
    }

    // if (types && types.length > 0) {
    //   articles
    //     .leftJoin('article.types', 'type_filter')
    //     .andWhere('type_filter.name IN (:...types)', { types })
    //     .groupBy('article.id')
    //     .having('COUNT(DISTINCT type_filter.name) = :typeCount', { typeCount: types.length });
    // }

    if (statuses && statuses.length > 0) {
      articles.andWhere('article.status IN (:...statuses)', { statuses });
    }

    const userClaimStatusArray = Array.isArray(userClaimStatus)
      ? userClaimStatus
      : [userClaimStatus];

    if (userClaimStatusArray && userClaimStatusArray.length) {
      articles.andWhere(
        new Brackets((qb) => {
          if (userClaimStatusArray.includes(UserArticleAnswerStatus.CLAIMED)) {
            qb.orWhere('userArticleAnswers.status = :claimed', {
              claimed: UserArticleAnswerStatus.CLAIMED,
            });
          }

          if (
            userClaimStatusArray.includes(UserArticleAnswerStatus.UNCLAIMED)
          ) {
            qb.orWhere(
              new Brackets((subQb) => {
                subQb
                  .where('userArticleAnswers.status = :unclaimed', {
                    unclaimed: UserArticleAnswerStatus.UNCLAIMED,
                  })
                  .orWhere('userArticleAnswers.status IS NULL');
              }),
            );
          }
        }),
      );
    }

    if (categoryId) {
      articles.andWhere('article.categoryId = :categoryId', { categoryId });
    }
    const paginatedResult = await Utils.paginate<any>(
      articles,
      requestData,
      true,
    );

    const mappedItems = plainToInstance(
      ArticleResponseDto,
      paginatedResult.items,
      {
        excludeExtraneousValues: true,
      },
    );

    return {
      items: mappedItems,
      meta: paginatedResult.meta,
    };
  }

  async detail(currentUser: UserPayload, id: number) {
    const article = await this.articleRepository
      .createQueryBuilder('article')
      .leftJoin('article.sponsor', 'sponsor')
      .leftJoin('article.types', 'type')
      .leftJoin(
        'user-article-answers',
        'uaa',
        'uaa.articleId = article.id AND uaa.userId = :userId',
        { userId: currentUser.id },
      )
      .select([
        'article.id AS article_id',
        'article.title AS article_title',
        'article.content AS article_content',
        'article.readTime AS article_read_time',
        'article.point AS article_point',
        'article.category AS article_category',
        'article.status AS article_status',
        'article.imageUrl AS article_image_url',

        'sponsor.id AS sponsor_id',
        'sponsor.name AS sponsor_name',
        'sponsor.imageUrl AS sponsor_image_url',

        "COALESCE(uaa.status, 'not_attempted') AS user_attempt_status",

        "GROUP_CONCAT(DISTINCT CONCAT(type.id, ':', type.name)) AS types_raw",
      ])
      .andWhere('article.id = :articleId', { articleId: id })
      .groupBy('article.id')
      .getRawOne();

    if (!article) {
      throw ApiError('', `Article with ID ${id} not found`);
    }

    return plainToInstance(ArticleDetailDto, article, {
      excludeExtraneousValues: true,
    });
  }

  async detailQuestions(currentUser: UserPayload, id: number) {
    const [article, questions] = await Promise.all([
      this.articleRepository
        .createQueryBuilder('article')
        .leftJoin('article.sponsor', 'sponsor')
        .leftJoin(
          'user-article-answers',
          'uaa',
          'uaa.articleId = article.id AND uaa.userId = :userId',
          { userId: currentUser.id },
        )
        .andWhere('article.id = :articleId', { articleId: id })
        .select([
          'article.id AS "articleId"',
          'article.title AS articleTitle',
          'article.status AS articleStatus',
          'article.point AS articlePoint',
          'sponsor.id AS sponsorId',
          'sponsor.name AS sponsorName',
          'sponsor.imageUrl AS sponsorImageUrl',
          "COALESCE(uaa.status, 'not_attempted') AS claimStatus",
        ])
        .getRawOne(),

      this.articleQuestionRepository
        .createQueryBuilder('question')
        .leftJoinAndSelect('question.articleQuestionAnswers', 'answers')
        .select([
          'question.id',
          'question.question',
          'question.type',
          'question.orderIndex',
          'answers.id',
          'answers.answer',
          'answers.orderIndex',
        ])
        .where('question.articleId = :articleId', { articleId: id })
        .orderBy('question.orderIndex', 'ASC')
        .addOrderBy('answers.orderIndex', 'ASC')
        .getMany(),
    ]);

    if (!questions.length) {
      throw ApiError('', `No questions found for article ${id}`);
    }

    return { article, questions };
  }

  async checkAnswers(
    currentUser: UserPayload,
    articleId: number,
    requestData: CheckAnswersDto,
  ) {
    const correctMap = await this.getCorrectAnswers(articleId);
    const correctCount = this.calculateCorrectCount(requestData, correctMap);

    const article = await this.articleRepository.findOne({
      where: { id: articleId },
    });

    if (!article) {
      throw ApiError('E902', `Article ${articleId} not found`);
    }

    if (article.status === ArticleStatus.END) {
      throw ApiError(
        'E28',
        'This article has ended. You cannot receive reward anymore.',
      );
    }

    let userArticleAnswer = await this.userArticleAnswerRepository.findOne({
      where: { userId: currentUser.id, articleId },
    });

    if (
      correctCount !== correctMap.size &&
      (!userArticleAnswer ||
        (userArticleAnswer &&
          userArticleAnswer.status === UserArticleAnswerStatus.UNCLAIMED))
    ) {
      return {
        totalQuestions: correctMap.size,
        correctCount,
        transaction: null,
      };
    } else if (
      (correctCount !== correctMap.size &&
        userArticleAnswer &&
        userArticleAnswer.status === UserArticleAnswerStatus.CLAIMED) ||
      (correctCount === correctMap.size &&
        userArticleAnswer &&
        userArticleAnswer.status === UserArticleAnswerStatus.CLAIMED)
    ) {
      return {
        totalQuestions: correctMap.size,
        correctCount,
        isAlreadyClaimed: true,
      };
    }

    await this.dataSource.transaction(async (manager) => {
      userArticleAnswer = await manager.findOne(UserArticleAnswer, {
        where: { userId: currentUser.id, articleId },
      });

      if (correctCount === correctMap.size && !userArticleAnswer) {
        userArticleAnswer = new UserArticleAnswer();
        userArticleAnswer.userId = currentUser.id;
        userArticleAnswer.articleId = articleId;
        userArticleAnswer.status = UserArticleAnswerStatus.UNCLAIMED;
        await manager.save(userArticleAnswer);
      }
    });

    const transaction = await this.transactionService.createTransaction(
      currentUser,
      {
        type: TransactionType.EARN,
        subType: TransactionSubType.LEARN,
        articleId,
      } as CreateTransactionDto,
    );

    return {
      totalQuestions: correctMap.size,
      correctCount,
      transaction: transaction || null,
    };
  }

  private async getCorrectAnswers(
    articleId: number,
  ): Promise<QuestionAnswerMap> {
    const correctAnswers = await this.articleQuestionAnswerRepository
      .createQueryBuilder('answer')
      .innerJoin('answer.articleQuestion', 'question')
      .where('question.articleId = :articleId', { articleId })
      .andWhere('answer.isCorrect = true')
      .select([
        'question.id AS questionId',
        'answer.orderIndex AS correctAnswerIndex',
        'question.type AS questionType',
      ])
      .getRawMany();

    const correctMap = new Map<
      number,
      { type: string; answers: Set<number> }
    >();
    correctAnswers.forEach((a) => {
      if (!correctMap.has(a.questionId)) {
        correctMap.set(a.questionId, {
          type: a.questionType,
          answers: new Set(),
        });
      }
      correctMap.get(a.questionId)?.answers.add(a.correctAnswerIndex);
    });

    return correctMap;
  }

  private calculateCorrectCount(
    requestData: CheckAnswersDto,
    correctMap: QuestionAnswerMap,
  ) {
    let correctCount = 0;
    requestData.answers.forEach(({ questionId, answers }) => {
      const correctData = correctMap.get(questionId);
      if (!correctData) return;

      const userAnswers = new Set(answers.map(Number));
      const correctAnswers = correctData.answers;
      if (
        userAnswers.size === correctAnswers.size &&
        [...userAnswers].every((ans) => correctAnswers.has(ans))
      ) {
        correctCount++;
      }
    });

    return correctCount;
  }
}
