import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { Chain } from 'src/entities/chain.entity';
import { ChainAdminController } from './chain.admin.controller';
import { ChainAdminService } from './chain.admin.service';
import { HotWallet } from 'src/entities/hot-wallet.entity';
import { ChainService } from './chain.service';
import { ChainController } from './chain.controller';

@Module({
  imports: [CommonModule, TypeOrmModule.forFeature([Chain, HotWallet])],
  providers: [ChainAdminService, ChainService],
  controllers: [ChainAdminController, ChainController],
  exports: [ChainAdminService],
})
export class ChainModule {}
