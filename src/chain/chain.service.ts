import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Chain } from 'src/entities/chain.entity';
import { UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { ChainResponseDto } from './dto/list-chain.dto';
import { Utils } from 'src/common/utils';
import { plainToInstance } from 'class-transformer';
import { PaginateChainDto } from './dto/paginate-chain.dto';

@Injectable()
export class ChainService {
  constructor(
    @InjectRepository(Chain)
    private readonly chainRepository: Repository<Chain>,
  ) {}

  async getAllChains(currentUser: UserPayload, requestData: PaginateChainDto) {
    const queryBuilder = this.chainRepository
      .createQueryBuilder('chains')
      .leftJoinAndSelect(
        'user-chains',
        'uc',
        'uc.chainId = chains.id AND uc.userId = :userId',
        { userId: currentUser.id },
      )
      .where('chains.isDeleted = false')
      .orderBy('chains.createdAt', 'DESC');

    const data = await Utils.paginate<any>(queryBuilder, requestData, true);

    return {
      ...data,
      items: plainToInstance(ChainResponseDto, data.items, {
        excludeExtraneousValues: true,
      }),
    };
  }
}
