import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { ChainType } from 'src/entities/chain.entity';

export class ChainResponseDto {
  @ApiProperty()
  @Expose({ name: 'chains_id' })
  id: number;

  @ApiProperty()
  @Expose({ name: 'chains_chainName' })
  chainName: string;

  @ApiProperty({ required: false })
  @Expose({ name: 'chains_imageUrl' })
  imageUrl?: string;

  @ApiProperty()
  @Expose({ name: 'chains_blockchainId' })
  blockchainId: string;

  @ApiProperty({ required: false })
  @Expose({ name: 'chains_factorySponsorContractAddress' })
  factorySponsorContractAddress?: string;

  @ApiProperty({ required: false })
  @Expose({ name: 'chains_voucherContractAddress' })
  voucherContractAddress?: string;

  @ApiProperty({ required: false })
  @Expose({ name: 'chains_gasSponsorContractAddress' })
  gasSponsorContractAddress?: string;

  @ApiProperty()
  @Expose({ name: 'chains_createdAt' })
  createdAt: Date;

  @ApiProperty()
  @Expose({ name: 'chains_updatedAt' })
  updatedAt: Date;

  @ApiProperty({
    required: false,
    description: 'Wallet address của user trên chain này (nếu có)',
  })
  @Expose({ name: 'uc_walletAddress' })
  walletAddress?: string;
}
