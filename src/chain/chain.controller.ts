import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { ChainService } from './chain.service';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { Http } from 'winston/lib/winston/transports';
import { User } from 'src/common/decorator/user.decorator';
import { ChainResponseDto } from './dto/list-chain.dto';
import { Pagination } from 'nestjs-typeorm-paginate';
import { UserPayload } from 'src/entities/user.entity';
import { PaginateChainDto } from './dto/paginate-chain.dto';

@ApiBearerAuth()
@Controller('chain')
export class ChainController {
  constructor(private readonly chainService: ChainService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get Chains' })
  async getAllChains(
    @User() currentUser: UserPayload,
    @Query() requestData: PaginateChainDto,
  ): Promise<Pagination<ChainResponseDto>> {
    return this.chainService.getAllChains(currentUser, requestData);
  }
}
