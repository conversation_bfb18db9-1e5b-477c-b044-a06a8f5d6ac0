import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { RedeemItem, RedeemItemStatus } from 'src/entities/redeem-item.entity';
import { UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';
import { FindRedeemItemsDto } from './dto/find-redeem-items.dto';

@Injectable()
export class RedeemItemsService {
  private readonly logger = new Logger(RedeemItemsService.name);
  constructor(
    @InjectRepository(RedeemItem)
    private redeemItemRepository: Repository<RedeemItem>,
  ) {}

  async getAll(currentUser: UserPayload, requestData: FindRedeemItemsDto) {
    const { redeemItemType, paidItemType, chainId } = requestData;
    const redeemItems = this.redeemItemRepository
      .createQueryBuilder('redeemItem')
      .leftJoin('redeemItem.asset', 'asset')
      .leftJoin('redeemItem.paidAsset', 'paidAsset')
      .leftJoin('redeemItem.chain', 'chain')
      .orderBy('redeemItem.createdAt', 'DESC')
      .select([
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.imageUrl',
        'redeemItem.point',
        'redeemItem.redeemedAmount',
        'redeemItem.description',
        'redeemItem.type',
        'redeemItem.paidType',
        'redeemItem.status',
        'redeemItem.chainId',
        'asset.id',
        'asset.type',
        'asset.name',
        'asset.image',
        'asset.symbol',
        'paidAsset.id',
        'paidAsset.type',
        'paidAsset.name',
        'paidAsset.image',
        'paidAsset.symbol',
        'chain.id',
        'chain.chainName',
        'chain.chainType',
        'chain.imageUrl',
      ])
      .where('redeemItem.status = :status', {
        status: RedeemItemStatus.ACTIVE,
      })
      .andWhere('redeemItem.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('redeemItem.remainReward > 0');

    if (chainId) {
      redeemItems.andWhere('redeemItem.chainId = :chainId', { chainId });
    }

    if (redeemItemType) {
      redeemItems.andWhere('redeemItem.type = :redeemItemType', {
        redeemItemType,
      });
    }
    if (paidItemType) {
      redeemItems.andWhere('redeemItem.paidType = :paidItemType', {
        paidItemType,
      });
    }

    return Utils.paginate<RedeemItem>(redeemItems, requestData);
  }

  async getDetail(id: number) {
    const redeemItem = this.redeemItemRepository
      .createQueryBuilder('redeemItem')
      .leftJoin('redeemItem.sponsor', 'sponsor')
      .leftJoin('redeemItem.asset', 'asset')
      .leftJoin('redeemItem.paidAsset', 'paidAsset')
      .leftJoin('redeemItem.chain', 'chain')
      .orderBy('redeemItem.createdAt', 'DESC')
      .select([
        'redeemItem.id',
        'redeemItem.name',
        'redeemItem.imageUrl',
        'redeemItem.point',
        'redeemItem.redeemedAmount',
        'redeemItem.description',
        'redeemItem.type',
        'redeemItem.paidType',
        'redeemItem.validFrom',
        'redeemItem.validTo',
        'redeemItem.status',
        'redeemItem.chainId',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
        'sponsor.description',
        'sponsor.website',
        'sponsor.email',
        'asset.id',
        'asset.type',
        'asset.name',
        'asset.image',
        'asset.symbol',
        'paidAsset.id',
        'paidAsset.type',
        'paidAsset.name',
        'paidAsset.image',
        'paidAsset.symbol',
        'chain.id',
        'chain.chainName',
        'chain.chainType',
        'chain.imageUrl',
      ])
      .where('redeemItem.id = :id', { id })
      .getOne();

    return redeemItem;
  }
}
