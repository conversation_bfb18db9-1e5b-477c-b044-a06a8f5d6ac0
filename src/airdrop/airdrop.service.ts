import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { Airdrop } from 'src/entities/airdrop.entity';
import { UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AirdropService {
  private readonly logger = new Logger(AirdropService.name);

  constructor(
    @InjectRepository(Airdrop)
    private airdropRepository: Repository<Airdrop>,
  ) {}

  async getAll(currentUser: UserPayload) {
    const airdrops = this.airdropRepository
      .createQueryBuilder('airdrops')
      .leftJoin('airdrops.airdropPhases', 'airdropPhases')
      .leftJoin('airdrops.sponsor', 'sponsor')
      .leftJoin('airdrops.chain', 'chain')
      .select([
        'airdrops.id',
        'airdrops.name',
        'airdrops.startDate',
        'airdrops.endDate',
        'airdrops.status',
        'airdropPhases.id',
        'airdropPhases.phaseOrder',
        'airdropPhases.milestonePoint',
        'airdropPhases.startDate',
        'airdropPhases.endDate',
        'airdropPhases.status',
        'chain.id',
        'chain.chainName',
        'chain.chainType',
        'chain.imageUrl',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
      ])
      .orderBy('airdrops.startDate', 'ASC')
      .addOrderBy('airdrops.name', 'ASC');

    return Utils.paginate<any>(airdrops, {}, false);
  }
}
