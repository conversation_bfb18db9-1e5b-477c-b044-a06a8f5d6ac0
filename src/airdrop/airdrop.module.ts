import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from 'src/common-services/common.module';
import { Airdrop } from 'src/entities/airdrop.entity';
import { AirdropController } from './airdrop.controller';
import { AirdropService } from './airdrop.service';

@Module({
  controllers: [AirdropController],
  providers: [AirdropService],
  imports: [TypeOrmModule.forFeature([Airdrop]), CommonModule],
})
export class AirdropModule {}
