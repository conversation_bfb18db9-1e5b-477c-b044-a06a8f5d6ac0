import {
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { AdminPayload } from 'src/entities/admin.entity';
import { AirdropService } from './airdrop.service';
import { AirdropAdminService } from './airdrop.admin.service';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiBearerAuth()
@ApiTags('Admin/Airdrops')
@UseGuards(JwtAuthGuard)
@Controller('admin/airdrops')
export class AirdropAdminController {
  constructor(private readonly airdropAdminService: AirdropAdminService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get airdrops' })
  async getAll(@User() currentUser: AdminPayload) {
    return await this.airdropAdminService.getAll(currentUser);
  }

  @Post('import-airdrops')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Import quests CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importAirdropsCSV(@UploadedFile() file: Express.Multer.File) {
    return this.airdropAdminService.importAirdropsCSV(file);
  }

  @Post('import-airdrop-phases')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Import airdrop phases CSV' })
  @UseInterceptors(FileInterceptor('file'))
  async importAirdropPhasesCSV(@UploadedFile() file: Express.Multer.File) {
    return this.airdropAdminService.importAirdropPhasesCSV(file);
  }
}
