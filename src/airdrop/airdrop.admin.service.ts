import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Utils } from 'src/common/utils';
import { AdminPayload } from 'src/entities/admin.entity';
import { AirdropPhase } from 'src/entities/airdrop-phase.entity';
import { Airdrop } from 'src/entities/airdrop.entity';
import { UserPayload } from 'src/entities/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AirdropAdminService {
  private readonly logger = new Logger(AirdropAdminService.name);

  constructor(
    @InjectRepository(Airdrop)
    private airdropRepository: Repository<Airdrop>,
    @InjectRepository(AirdropPhase)
    private airdropPhaseRepository: Repository<AirdropPhase>,
  ) {}

  async getAll(currentUser: AdminPayload) {
    const airdrops = this.airdropRepository
      .createQueryBuilder('airdrops')
      .leftJoin('airdrops.airdropPhases', 'airdropPhases')
      .leftJoin('airdrops.sponsor', 'sponsor')
      .leftJoin('airdrops.chain', 'chain')
      .select([
        'airdrops.id',
        'airdrops.name',
        'airdrops.startDate',
        'airdrops.endDate',
        'airdrops.status',
        'airdropPhases.id',
        'airdropPhases.phaseOrder',
        'airdropPhases.milestonePoint',
        'airdropPhases.startDate',
        'airdropPhases.endDate',
        'airdropPhases.status',
        'chain.id',
        'chain.chainName',
        'chain.chainType',
        'chain.imageUrl',
        'sponsor.id',
        'sponsor.name',
        'sponsor.imageUrl',
      ])
      .orderBy('airdrops.startDate', 'ASC')
      .addOrderBy('airdrops.name', 'ASC');

    return Utils.paginate<any>(airdrops, {}, false);
  }

  async importAirdropsCSV(file: Express.Multer.File) {
    try {
      // TODO: Deploy reward contract (rewardContractAddress)
      const rows = (await Utils.readCsv(file)) as any[];
      const airdrops: Partial<Airdrop>[] = rows.map((row) => {
        // TODO: Deploy reward contract (rewardContractAddress)


        return {
          name: row['name'],
          description: row['description'],
          chainId: +row['chainId'],
          sponsorId: +row['sponsorId'],
          assetId: +row['assetId'],
          rewardAmount: row['rewardAmount'],
          // TODO: Deploy reward contract (rewardContractAddress)
          // rewardContractAddress
          convertedPoints: row['convertedPoints'],
          startDate: row['startDate'],
          endDate: row['endDate'],
        };
      });

      await this.airdropRepository.upsert(airdrops, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async importAirdropPhasesCSV(file: Express.Multer.File) {
    try {
      // TODO: Deploy reward contract (rewardContractAddress)
      const rows = (await Utils.readCsv(file)) as any[];
      const airdropPhases: Partial<AirdropPhase>[] = rows.map((row) => {
        return {
          airdropId: row['airdropId'],
          phaseOrder: +row['phaseOrder'],
          milestonePoint: row['milestonePoint'],
          startDate: row['startDate'],
          endDate: row['endDate'],
        };
      });

      await this.airdropPhaseRepository.upsert(airdropPhases, {
        conflictPaths: ['id'],
        skipUpdateIfNoValuesChanged: true,
      });

      return true;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
