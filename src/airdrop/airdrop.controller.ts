import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/guard/jwt-auth.guard';
import { User } from 'src/common/decorator/user.decorator';
import { UserPayload } from 'src/entities/user.entity';
import { AirdropService } from './airdrop.service';

@ApiBearerAuth()
@ApiTags('Airdrops')
@UseGuards(JwtAuthGuard)
@Controller('airdrops')
export class AirdropController {
  constructor(private readonly airdropService: AirdropService) {}

  @ApiOperation({ summary: 'Get airdrops' })
  @Get()
  async getAll(@User() currentUser: UserPayload) {
    return await this.airdropService.getAll(currentUser);
  }
}
